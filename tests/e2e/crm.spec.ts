import { test, expect } from '@playwright/test';

test.describe('CRM - Client Management', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to CRM page
    await page.goto('/en/crm');

    // Wait for the page to load
    await page.waitForLoadState('networkidle');
  });

  test('should display CRM page with glass morphism design', async ({
    page,
  }) => {
    // Check page title
    await expect(page.locator('h1')).toContainText('Client Management');

    // Check for glass morphism elements
    await expect(page.locator('[class*="backdrop-blur"]')).toBeVisible();

    // Check for gradient background
    await expect(page.locator('body')).toHaveCSS('background', /gradient/);
  });

  test('should show empty state when no clients exist', async ({ page }) => {
    // Look for empty state message
    await expect(page.locator('text=No clients found')).toBeVisible();
    await expect(page.locator('text=Add Your First Client')).toBeVisible();
  });

  test('should open client creation form', async ({ page }) => {
    // Click on "Add Client" or "Add Your First Client" button
    const addButton = page
      .locator(
        'button:has-text("Add Client"), button:has-text("Add Your First Client")',
      )
      .first();
    await addButton.click();

    // Check if modal opened
    await expect(page.locator('text=Create New Client')).toBeVisible();

    // Check for form fields
    await expect(page.locator('label:has-text("Client Type")')).toBeVisible();
    await expect(page.locator('label:has-text("Country")')).toBeVisible();
    await expect(page.locator('label:has-text("Name")')).toBeVisible();
  });

  test('should create a Czech client with ARES integration', async ({
    page,
  }) => {
    // Open client creation form
    const addButton = page
      .locator(
        'button:has-text("Add Client"), button:has-text("Add Your First Client")',
      )
      .first();
    await addButton.click();

    // Fill basic information
    await page.selectOption('select[label="Client Type"]', 'company');
    await page.selectOption('select[label="Country"]', 'CZ');
    await page.fill('input[label="Name"]', 'Test Czech Company');

    // Fill Czech-specific fields
    await page.fill('input[label="IČO"]', '25596641'); // Valid ICO

    // Check if ARES button appears
    await expect(
      page.locator('button:has-text("Fetch from ARES")'),
    ).toBeVisible();

    // Fill address
    await page.fill('input[label="City"]', 'Prague');
    await page.fill('input[label="Street"]', 'Test Street 123');
    await page.fill('input[label="Postal Code"]', '11000');

    // Fill contact information
    await page.fill('input[placeholder="Contact name"]', 'John Doe');
    await page.fill(
      'input[placeholder="<EMAIL>"]',
      '<EMAIL>',
    );

    // Submit form
    await page.click('button:has-text("Create Client")');

    // Wait for success (form should close)
    await expect(page.locator('text=Create New Client')).not.toBeVisible();

    // Check if client appears in the list
    await expect(page.locator('text=Test Czech Company')).toBeVisible();
  });

  test('should create an EU client with VAT number', async ({ page }) => {
    // Open client creation form
    const addButton = page
      .locator(
        'button:has-text("Add Client"), button:has-text("Add Your First Client")',
      )
      .first();
    await addButton.click();

    // Fill basic information
    await page.selectOption('select[label="Client Type"]', 'company');
    await page.selectOption('select[label="Country"]', 'DE');
    await page.fill('input[label="Name"]', 'German Test GmbH');

    // Fill EU-specific fields
    await page.fill('input[label="VAT Number"]', 'DE123456789');
    await page.selectOption('select[label="VAT Status"]', 'vat_payer');

    // Fill address
    await page.fill('input[label="City"]', 'Berlin');

    // Fill contact information
    await page.fill('input[placeholder="Contact name"]', 'Hans Mueller');
    await page.fill(
      'input[placeholder="<EMAIL>"]',
      '<EMAIL>',
    );

    // Submit form
    await page.click('button:has-text("Create Client")');

    // Wait for success
    await expect(page.locator('text=Create New Client')).not.toBeVisible();

    // Check if client appears in the list
    await expect(page.locator('text=German Test GmbH')).toBeVisible();
    await expect(page.locator('text=Germany')).toBeVisible();
  });

  test('should create a non-EU client with tax ID', async ({ page }) => {
    // Open client creation form
    const addButton = page
      .locator(
        'button:has-text("Add Client"), button:has-text("Add Your First Client")',
      )
      .first();
    await addButton.click();

    // Fill basic information
    await page.selectOption('select[label="Client Type"]', 'freelancer');
    await page.selectOption('select[label="Country"]', 'US');
    await page.fill('input[label="Name"]', 'John Smith');

    // Fill non-EU specific fields
    await page.fill('input[label="Tax ID"]', 'US123456789');

    // Fill address
    await page.fill('input[label="City"]', 'New York');

    // Fill contact information
    await page.fill('input[placeholder="Contact name"]', 'John Smith');
    await page.fill(
      'input[placeholder="<EMAIL>"]',
      '<EMAIL>',
    );

    // Submit form
    await page.click('button:has-text("Create Client")');

    // Wait for success
    await expect(page.locator('text=Create New Client')).not.toBeVisible();

    // Check if client appears in the list
    await expect(page.locator('text=John Smith')).toBeVisible();
    await expect(page.locator('text=United States')).toBeVisible();
  });

  test('should validate required fields', async ({ page }) => {
    // Open client creation form
    const addButton = page
      .locator(
        'button:has-text("Add Client"), button:has-text("Add Your First Client")',
      )
      .first();
    await addButton.click();

    // Try to submit without filling required fields
    await page.click('button:has-text("Create Client")');

    // Check for validation errors
    await expect(page.locator('text=Name is required')).toBeVisible();
    await expect(page.locator('text=City is required')).toBeVisible();
  });

  test('should filter clients by country', async ({ page }) => {
    // Assuming we have clients from different countries
    // This test would need existing data or previous test setup

    // Use country filter
    await page.selectOption('select:near(text="All Countries")', 'CZ');

    // Wait for filter to apply
    await page.waitForTimeout(1000);

    // All visible clients should be from Czech Republic
    const countryBadges = page.locator('[class*="badge"]:has-text("Czechia")');
    const clientCards = page.locator(
      '[class*="cursor-pointer"]:has([class*="badge"])',
    );

    if ((await clientCards.count()) > 0) {
      expect(await countryBadges.count()).toBeGreaterThan(0);
    }
  });

  test('should search clients by name', async ({ page }) => {
    // Use search functionality
    await page.fill('input[placeholder="Search clients..."]', 'Test');

    // Wait for search to apply (debounced)
    await page.waitForTimeout(1000);

    // Results should contain the search term
    const clientNames = page.locator('h3:near([class*="badge"])');
    const count = await clientNames.count();

    for (let i = 0; i < count; i++) {
      const text = await clientNames.nth(i).textContent();
      expect(text?.toLowerCase()).toContain('test');
    }
  });

  test('should open client details', async ({ page }) => {
    // Assuming we have at least one client
    const firstClient = page
      .locator('[class*="cursor-pointer"]:has(h3)')
      .first();

    if (await firstClient.isVisible()) {
      await firstClient.click();

      // Should navigate to client details
      await expect(
        page.locator('button:has-text("← Back to List")'),
      ).toBeVisible();
      await expect(
        page.locator('button:has-text("Edit Client")'),
      ).toBeVisible();
    }
  });

  test('should edit client information', async ({ page }) => {
    // Assuming we have at least one client
    const editButton = page.locator('button:has-text("Edit")').first();

    if (await editButton.isVisible()) {
      await editButton.click();

      // Should open edit modal
      await expect(page.locator('text=Edit Client')).toBeVisible();

      // Modify client name
      const nameInput = page.locator('input[label="Name"]');
      await nameInput.fill('Updated Client Name');

      // Save changes
      await page.click('button:has-text("Update Client")');

      // Wait for success
      await expect(page.locator('text=Edit Client')).not.toBeVisible();

      // Check if changes are reflected
      await expect(page.locator('text=Updated Client Name')).toBeVisible();
    }
  });

  test('should handle form cancellation', async ({ page }) => {
    // Open client creation form
    const addButton = page
      .locator(
        'button:has-text("Add Client"), button:has-text("Add Your First Client")',
      )
      .first();
    await addButton.click();

    // Fill some data
    await page.fill('input[label="Name"]', 'Test Client');

    // Cancel form
    await page.click('button:has-text("Cancel")');

    // Modal should close
    await expect(page.locator('text=Create New Client')).not.toBeVisible();

    // Should return to client list
    await expect(
      page.locator('h1:has-text("Client Management")'),
    ).toBeVisible();
  });

  test('should display client statistics', async ({ page }) => {
    // Check for statistics cards
    await expect(page.locator('text=Total Clients')).toBeVisible();
    await expect(page.locator('text=Countries')).toBeVisible();
    await expect(page.locator('text=Companies')).toBeVisible();
    await expect(page.locator('text=Freelancers')).toBeVisible();
  });

  test('should handle multiple contacts per client', async ({ page }) => {
    // Open client creation form
    const addButton = page
      .locator(
        'button:has-text("Add Client"), button:has-text("Add Your First Client")',
      )
      .first();
    await addButton.click();

    // Fill basic information
    await page.fill('input[label="Name"]', 'Multi Contact Client');
    await page.selectOption('select[label="Country"]', 'CZ');
    await page.fill('input[label="City"]', 'Prague');

    // Fill first contact
    await page.fill('input[placeholder="Contact name"]', 'John Doe');
    await page.fill(
      'input[placeholder="<EMAIL>"]',
      '<EMAIL>',
    );

    // Add second contact
    await page.click('button:has-text("Add Contact")');

    // Fill second contact
    const contactInputs = page.locator('input[placeholder="Contact name"]');
    await contactInputs.nth(1).fill('Jane Smith');

    const emailInputs = page.locator(
      'input[placeholder="<EMAIL>"]',
    );
    await emailInputs.nth(1).fill('<EMAIL>');

    // Submit form
    await page.click('button:has-text("Create Client")');

    // Wait for success
    await expect(page.locator('text=Create New Client')).not.toBeVisible();

    // Check if client appears with contact count
    await expect(page.locator('text=Multi Contact Client')).toBeVisible();
    await expect(page.locator('text=2 contacts')).toBeVisible();
  });

  test('should validate email format', async ({ page }) => {
    // Open client creation form
    const addButton = page
      .locator(
        'button:has-text("Add Client"), button:has-text("Add Your First Client")',
      )
      .first();
    await addButton.click();

    // Fill basic information
    await page.fill('input[label="Name"]', 'Test Client');
    await page.selectOption('select[label="Country"]', 'CZ');
    await page.fill('input[label="City"]', 'Prague');

    // Fill invalid email
    await page.fill(
      'input[placeholder="<EMAIL>"]',
      'invalid-email',
    );
    await page.fill('input[placeholder="Contact name"]', 'John Doe');

    // Try to submit
    await page.click('button:has-text("Create Client")');

    // Should show validation error
    await expect(page.locator('text=Invalid email format')).toBeVisible();
  });
});
