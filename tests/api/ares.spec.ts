import { test, expect } from '@playwright/test';
import { ARESResponse } from '../../src/types/crm';

// Mock authentication token
const mockToken = 'mock-jwt-token';

test.describe('ARES API', () => {
  test.describe('POST /api/ares', () => {
    test('should validate ICO format', async ({ request }) => {
      const response = await request.post('/api/ares', {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mockToken}`,
        },
        data: {
          ico: '1234567', // Invalid ICO (7 digits)
        },
      });

      expect(response.status()).toBe(400);

      const result: ARESResponse = await response.json();
      expect(result.success).toBe(false);
      expect(result.error).toContain('ICO must be 8 digits');
    });

    test('should validate ICO checksum', async ({ request }) => {
      const response = await request.post('/api/ares', {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `<PERSON><PERSON> ${mockToken}`,
        },
        data: {
          ico: '12345678', // Invalid checksum
        },
      });

      expect(response.status()).toBe(400);

      const result: ARESResponse = await response.json();
      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid ICO checksum');
    });

    test('should require ICO parameter', async ({ request }) => {
      const response = await request.post('/api/ares', {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mockToken}`,
        },
        data: {},
      });

      expect(response.status()).toBe(400);

      const result: ARESResponse = await response.json();
      expect(result.success).toBe(false);
      expect(result.error).toContain('ICO is required');
    });

    test('should require authorization', async ({ request }) => {
      const response = await request.post('/api/ares', {
        headers: {
          'Content-Type': 'application/json',
          // Missing Authorization header
        },
        data: {
          ico: '25596641', // Valid ICO with correct checksum
        },
      });

      expect(response.status()).toBe(401);

      const result: ARESResponse = await response.json();
      expect(result.success).toBe(false);
      expect(result.error).toContain('Authorization required');
    });

    test('should handle valid ICO format', async ({ request }) => {
      // Note: This test might fail if ARES service is down or the ICO doesn't exist
      // In a real test environment, you might want to mock the ARES service
      const response = await request.post('/api/ares', {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mockToken}`,
        },
        data: {
          ico: '25596641', // Valid ICO with correct checksum (Google Czech Republic)
        },
      });

      // The response could be either success or failure depending on ARES availability
      expect([200, 500]).toContain(response.status());

      const result: ARESResponse = await response.json();

      if (response.status() === 200 && result.success) {
        // If successful, verify the response structure
        expect(result.data).toBeDefined();
        expect(result.data!.ico).toBe('25596641');
        expect(result.data!.name).toBeDefined();
        expect(result.data!.address).toBeDefined();
        expect(result.data!.address.city).toBeDefined();
        expect(result.data!.address.country).toBe('CZ');
        expect(result.data!.vat_status).toBeDefined();
      } else {
        // If failed, it should have an error message
        expect(result.success).toBe(false);
        expect(result.error).toBeDefined();
      }
    });

    test('should clean ICO input (remove spaces and non-digits)', async ({
      request,
    }) => {
      const response = await request.post('/api/ares', {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mockToken}`,
        },
        data: {
          ico: '255 966 41', // ICO with spaces
        },
      });

      // Should process the cleaned ICO (25596641)
      expect([200, 400, 500]).toContain(response.status());

      const result: ARESResponse = await response.json();

      if (response.status() === 400) {
        // If validation fails, it should be due to checksum, not format
        expect(result.error).not.toContain('ICO must be 8 digits');
      }
    });
  });

  test.describe('GET /api/ares', () => {
    test('should work with GET method', async ({ request }) => {
      const response = await request.get('/api/ares?ico=25596641', {
        headers: {
          Authorization: `Bearer ${mockToken}`,
        },
      });

      // The response could be either success or failure depending on ARES availability
      expect([200, 400, 500]).toContain(response.status());

      const result: ARESResponse = await response.json();

      if (response.status() === 200 && result.success) {
        expect(result.data).toBeDefined();
        expect(result.data!.ico).toBe('25596641');
      } else {
        expect(result.success).toBe(false);
        expect(result.error).toBeDefined();
      }
    });

    test('should require ICO parameter in GET', async ({ request }) => {
      const response = await request.get('/api/ares', {
        headers: {
          Authorization: `Bearer ${mockToken}`,
        },
      });

      expect(response.status()).toBe(400);

      const result: ARESResponse = await response.json();
      expect(result.success).toBe(false);
      expect(result.error).toContain('ICO parameter is required');
    });

    test('should validate ICO in GET request', async ({ request }) => {
      const response = await request.get('/api/ares?ico=1234567', {
        headers: {
          Authorization: `Bearer ${mockToken}`,
        },
      });

      expect(response.status()).toBe(400);

      const result: ARESResponse = await response.json();
      expect(result.success).toBe(false);
      expect(result.error).toContain('ICO must be 8 digits');
    });
  });

  test.describe('Error Handling', () => {
    test('should handle network errors gracefully', async ({ request }) => {
      // This test simulates what happens when ARES service is unavailable
      // In a real scenario, you might want to mock the fetch to simulate network errors

      const response = await request.post('/api/ares', {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mockToken}`,
        },
        data: {
          ico: '00000001', // ICO that likely doesn't exist
        },
      });

      // Should handle the error gracefully
      expect([200, 500]).toContain(response.status());

      const result: ARESResponse = await response.json();

      if (!result.success) {
        expect(result.error).toBeDefined();
        expect(typeof result.error).toBe('string');
      }
    });

    test('should handle malformed requests', async ({ request }) => {
      const response = await request.post('/api/ares', {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mockToken}`,
        },
        data: 'invalid json',
      });

      expect(response.status()).toBe(500);

      const result: ARESResponse = await response.json();
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });
  });
});
