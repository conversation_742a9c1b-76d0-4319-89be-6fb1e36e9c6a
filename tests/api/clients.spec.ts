import { test, expect } from '@playwright/test';
import {
  ClientFormData,
  CRMResponse,
  ClientWithRelations,
} from '../../src/types/crm';

// Test data
const testClient: ClientFormData = {
  type: 'company',
  name: 'Test Company Ltd.',
  country: 'CZ',
  street: 'Test Street 123',
  city: 'Prague',
  postal_code: '11000',
  use_same_address: true,
  ico: '********',
  dic: 'CZ********',
  vat_status: 'vat_payer',
  bank_name: 'Test Bank',
  iban: '************************',
  invoice_email: '<EMAIL>',
  notes: 'Test client for API testing',
  tags: ['test', 'api'],
  contacts: [
    {
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+420********9',
      position: 'CEO',
      is_default_contact: true,
    },
  ],
};

const testClientEU: ClientFormData = {
  type: 'company',
  name: 'German Test GmbH',
  country: 'DE',
  city: 'Berlin',
  use_same_address: true,
  vat_number: 'DE********9',
  vat_status: 'vat_payer',
  contacts: [
    {
      name: '<PERSON>eller',
      email: '<EMAIL>',
      is_default_contact: true,
    },
  ],
};

const testClientNonEU: ClientFormData = {
  type: 'freelancer',
  name: 'John Smith',
  country: 'US',
  city: 'New York',
  use_same_address: true,
  tax_id: 'US********9',
  contacts: [
    {
      name: 'John Smith',
      email: '<EMAIL>',
      is_default_contact: true,
    },
  ],
};

// Mock authentication token
const mockToken = 'mock-jwt-token';

test.describe('Clients API', () => {
  let createdClientIds: string[] = [];

  // Cleanup after tests
  test.afterEach(async ({ request }) => {
    // Clean up created clients
    for (const clientId of createdClientIds) {
      try {
        await request.delete(`/api/clients/${clientId}`, {
          headers: {
            Authorization: `Bearer ${mockToken}`,
          },
        });
      } catch (error) {
        console.log(`Failed to cleanup client ${clientId}:`, error);
      }
    }
    createdClientIds = [];
  });

  test.describe('POST /api/clients', () => {
    test('should create a Czech client with ICO and DIC', async ({
      request,
    }) => {
      const response = await request.post('/api/clients', {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mockToken}`,
        },
        data: testClient,
      });

      expect(response.status()).toBe(200);

      const result: CRMResponse<ClientWithRelations> = await response.json();
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();

      const client = result.data!;
      createdClientIds.push(client.id);

      // Verify client data
      expect(client.name).toBe(testClient.name);
      expect(client.country).toBe(testClient.country);
      expect(client.ico).toBe(testClient.ico);
      expect(client.dic).toBe(testClient.dic);
      expect(client.vat_status).toBe(testClient.vat_status);

      // Verify contacts
      expect(client.contacts).toHaveLength(1);
      expect(client.contacts[0].name).toBe(testClient.contacts[0].name);
      expect(client.contacts[0].is_default_contact).toBe(true);
    });

    test('should create an EU client with VAT number', async ({ request }) => {
      const response = await request.post('/api/clients', {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mockToken}`,
        },
        data: testClientEU,
      });

      expect(response.status()).toBe(200);

      const result: CRMResponse<ClientWithRelations> = await response.json();
      expect(result.success).toBe(true);

      const client = result.data!;
      createdClientIds.push(client.id);

      expect(client.name).toBe(testClientEU.name);
      expect(client.country).toBe(testClientEU.country);
      expect(client.vat_number).toBe(testClientEU.vat_number);
      expect(client.vat_status).toBe(testClientEU.vat_status);
    });

    test('should create a non-EU client with tax ID', async ({ request }) => {
      const response = await request.post('/api/clients', {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mockToken}`,
        },
        data: testClientNonEU,
      });

      expect(response.status()).toBe(200);

      const result: CRMResponse<ClientWithRelations> = await response.json();
      expect(result.success).toBe(true);

      const client = result.data!;
      createdClientIds.push(client.id);

      expect(client.name).toBe(testClientNonEU.name);
      expect(client.country).toBe(testClientNonEU.country);
      expect(client.tax_id).toBe(testClientNonEU.tax_id);
    });

    test('should validate required fields', async ({ request }) => {
      const invalidClient = {
        type: 'company',
        // Missing required fields: name, country, city
        contacts: [],
      };

      const response = await request.post('/api/clients', {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mockToken}`,
        },
        data: invalidClient,
      });

      expect(response.status()).toBe(400);

      const result: CRMResponse = await response.json();
      expect(result.success).toBe(false);
      expect(result.error).toContain('Validation failed');
    });

    test('should validate ICO format for Czech clients', async ({
      request,
    }) => {
      const invalidClient = {
        ...testClient,
        ico: '1234567', // Invalid ICO (7 digits instead of 8)
      };

      const response = await request.post('/api/clients', {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mockToken}`,
        },
        data: invalidClient,
      });

      expect(response.status()).toBe(400);

      const result: CRMResponse = await response.json();
      expect(result.success).toBe(false);
      expect(result.details).toContain('Invalid ICO format');
    });

    test('should validate DIC format for Czech clients', async ({
      request,
    }) => {
      const invalidClient = {
        ...testClient,
        dic: 'CZ1234567', // Invalid DIC (7 digits instead of 8-10)
      };

      const response = await request.post('/api/clients', {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mockToken}`,
        },
        data: invalidClient,
      });

      expect(response.status()).toBe(400);

      const result: CRMResponse = await response.json();
      expect(result.success).toBe(false);
      expect(result.details).toContain('Invalid DIC format');
    });

    test('should require authorization', async ({ request }) => {
      const response = await request.post('/api/clients', {
        headers: {
          'Content-Type': 'application/json',
          // Missing Authorization header
        },
        data: testClient,
      });

      expect(response.status()).toBe(401);

      const result: CRMResponse = await response.json();
      expect(result.success).toBe(false);
      expect(result.error).toContain('Authorization required');
    });
  });

  test.describe('GET /api/clients', () => {
    test('should list clients with pagination', async ({ request }) => {
      // First create a client
      const createResponse = await request.post('/api/clients', {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mockToken}`,
        },
        data: testClient,
      });

      const createResult: CRMResponse<ClientWithRelations> =
        await createResponse.json();
      createdClientIds.push(createResult.data!.id);

      // Then list clients
      const response = await request.get('/api/clients?limit=10&page=1', {
        headers: {
          Authorization: `Bearer ${mockToken}`,
        },
      });

      expect(response.status()).toBe(200);

      const result: CRMResponse = await response.json();
      expect(result.success).toBe(true);
      expect(result.data.clients).toBeDefined();
      expect(result.data.stats).toBeDefined();
      expect(result.pagination).toBeDefined();
    });

    test('should filter clients by country', async ({ request }) => {
      // Create clients from different countries
      const czResponse = await request.post('/api/clients', {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mockToken}`,
        },
        data: testClient,
      });
      const czResult: CRMResponse<ClientWithRelations> =
        await czResponse.json();
      createdClientIds.push(czResult.data!.id);

      const deResponse = await request.post('/api/clients', {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mockToken}`,
        },
        data: testClientEU,
      });
      const deResult: CRMResponse<ClientWithRelations> =
        await deResponse.json();
      createdClientIds.push(deResult.data!.id);

      // Filter by Czech clients
      const response = await request.get('/api/clients?country=CZ', {
        headers: {
          Authorization: `Bearer ${mockToken}`,
        },
      });

      expect(response.status()).toBe(200);

      const result: CRMResponse = await response.json();
      expect(result.success).toBe(true);

      // All returned clients should be from Czech Republic
      result.data.clients.forEach((client: ClientWithRelations) => {
        expect(client.country).toBe('CZ');
      });
    });

    test('should search clients by name', async ({ request }) => {
      // Create a client
      const createResponse = await request.post('/api/clients', {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mockToken}`,
        },
        data: testClient,
      });
      const createResult: CRMResponse<ClientWithRelations> =
        await createResponse.json();
      createdClientIds.push(createResult.data!.id);

      // Search for the client
      const response = await request.get('/api/clients?search=Test Company', {
        headers: {
          Authorization: `Bearer ${mockToken}`,
        },
      });

      expect(response.status()).toBe(200);

      const result: CRMResponse = await response.json();
      expect(result.success).toBe(true);
      expect(result.data.clients.length).toBeGreaterThan(0);

      // At least one client should match the search
      const foundClient = result.data.clients.find(
        (client: ClientWithRelations) => client.name.includes('Test Company'),
      );
      expect(foundClient).toBeDefined();
    });
  });

  test.describe('GET /api/clients/:id', () => {
    test('should get a specific client', async ({ request }) => {
      // Create a client first
      const createResponse = await request.post('/api/clients', {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mockToken}`,
        },
        data: testClient,
      });

      const createResult: CRMResponse<ClientWithRelations> =
        await createResponse.json();
      const clientId = createResult.data!.id;
      createdClientIds.push(clientId);

      // Get the client
      const response = await request.get(`/api/clients/${clientId}`, {
        headers: {
          Authorization: `Bearer ${mockToken}`,
        },
      });

      expect(response.status()).toBe(200);

      const result: CRMResponse<ClientWithRelations> = await response.json();
      expect(result.success).toBe(true);
      expect(result.data.id).toBe(clientId);
      expect(result.data.name).toBe(testClient.name);
    });

    test('should return 404 for non-existent client', async ({ request }) => {
      const response = await request.get('/api/clients/non-existent-id', {
        headers: {
          Authorization: `Bearer ${mockToken}`,
        },
      });

      expect(response.status()).toBe(404);

      const result: CRMResponse = await response.json();
      expect(result.success).toBe(false);
      expect(result.error).toContain('Client not found');
    });
  });

  test.describe('PUT /api/clients/:id', () => {
    test('should update a client', async ({ request }) => {
      // Create a client first
      const createResponse = await request.post('/api/clients', {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mockToken}`,
        },
        data: testClient,
      });

      const createResult: CRMResponse<ClientWithRelations> =
        await createResponse.json();
      const clientId = createResult.data!.id;
      createdClientIds.push(clientId);

      // Update the client
      const updatedData = {
        ...testClient,
        name: 'Updated Test Company Ltd.',
        notes: 'Updated notes',
      };

      const response = await request.put(`/api/clients/${clientId}`, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mockToken}`,
        },
        data: updatedData,
      });

      expect(response.status()).toBe(200);

      const result: CRMResponse<ClientWithRelations> = await response.json();
      expect(result.success).toBe(true);
      expect(result.data.name).toBe('Updated Test Company Ltd.');
      expect(result.data.notes).toBe('Updated notes');
    });
  });

  test.describe('DELETE /api/clients/:id', () => {
    test('should delete a client', async ({ request }) => {
      // Create a client first
      const createResponse = await request.post('/api/clients', {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mockToken}`,
        },
        data: testClient,
      });

      const createResult: CRMResponse<ClientWithRelations> =
        await createResponse.json();
      const clientId = createResult.data!.id;

      // Delete the client
      const response = await request.delete(`/api/clients/${clientId}`, {
        headers: {
          Authorization: `Bearer ${mockToken}`,
        },
      });

      expect(response.status()).toBe(200);

      const result: CRMResponse = await response.json();
      expect(result.success).toBe(true);

      // Verify client is deleted
      const getResponse = await request.get(`/api/clients/${clientId}`, {
        headers: {
          Authorization: `Bearer ${mockToken}`,
        },
      });
      expect(getResponse.status()).toBe(404);
    });
  });
});
