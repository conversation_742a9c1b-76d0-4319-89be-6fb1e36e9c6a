import { test as setup, expect } from '@playwright/test';

const authFile = 'playwright/.auth/user.json';

setup('authenticate', async ({ page, request }) => {
  // Mock authentication for testing
  // In a real scenario, you would perform actual login

  // Navigate to login page
  await page.goto('/en/auth/login');

  // Mock the authentication by setting localStorage
  await page.evaluate(() => {
    localStorage.setItem('access_token', 'mock-jwt-token');
    localStorage.setItem(
      'user_profile',
      JSON.stringify({
        id: 'test-user-id',
        email: '<EMAIL>',
        full_name: 'Test User',
        account_type: 'company',
      }),
    );
    localStorage.setItem(
      'current_organization',
      JSON.stringify({
        id: 'test-org-id',
        name: 'Test Organization',
        account_type: 'company',
      }),
    );
  });

  // Navigate to a protected page to verify authentication
  await page.goto('/en/crm');

  // Wait for the page to load and verify we're authenticated
  await expect(page.locator('h1:has-text("Client Management")')).toBeVisible();

  // Save authentication state
  await page.context().storageState({ path: authFile });
});

// Mock API responses for testing
setup('mock api responses', async ({ page }) => {
  // Mock the clients API
  await page.route('/api/clients', async (route) => {
    const method = route.request().method();

    if (method === 'GET') {
      // Mock client list response
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            clients: [],
            stats: {
              total_clients: 0,
              by_country: {},
              by_type: {},
              by_vat_status: {},
              recent_clients: [],
            },
          },
          pagination: {
            page: 1,
            limit: 20,
            total: 0,
            total_pages: 0,
            has_next: false,
            has_prev: false,
          },
        }),
      });
    } else if (method === 'POST') {
      // Mock client creation response
      const requestBody = await route.request().postDataJSON();

      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            id: 'mock-client-id',
            organization_id: 'test-org-id',
            ...requestBody,
            contacts: requestBody.contacts || [],
            country_specifics: [],
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          },
          message: 'Client created successfully',
        }),
      });
    }
  });

  // Mock individual client API
  await page.route('/api/clients/*', async (route) => {
    const method = route.request().method();
    const url = route.request().url();
    const clientId = url.split('/').pop();

    if (method === 'GET') {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            id: clientId,
            organization_id: 'test-org-id',
            type: 'company',
            name: 'Mock Client',
            country: 'CZ',
            city: 'Prague',
            contacts: [],
            country_specifics: [],
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          },
        }),
      });
    } else if (method === 'PUT') {
      const requestBody = await route.request().postDataJSON();

      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            id: clientId,
            organization_id: 'test-org-id',
            ...requestBody,
            contacts: requestBody.contacts || [],
            country_specifics: [],
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          },
          message: 'Client updated successfully',
        }),
      });
    } else if (method === 'DELETE') {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          message: 'Client deleted successfully',
        }),
      });
    }
  });

  // Mock ARES API
  await page.route('/api/ares', async (route) => {
    const method = route.request().method();
    let ico: string;

    if (method === 'POST') {
      const requestBody = await route.request().postDataJSON();
      ico = requestBody.ico;
    } else {
      const url = new URL(route.request().url());
      ico = url.searchParams.get('ico') || '';
    }

    // Validate ICO format
    if (!ico || ico.length !== 8 || !/^[0-9]{8}$/.test(ico)) {
      await route.fulfill({
        status: 400,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          error: 'ICO must be 8 digits',
        }),
      });
      return;
    }

    // Mock successful ARES response for valid ICO
    if (ico === '25596641') {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            ico: '25596641',
            name: 'Google Czech Republic s.r.o.',
            dic: 'CZ25596641',
            address: {
              street: 'Rybná 716/24',
              city: 'Praha',
              postal_code: '11000',
              country: 'CZ',
            },
            vat_status: 'vat_payer',
            registration_number: '25596641',
          },
        }),
      });
    } else {
      // Mock "not found" response for other ICOs
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          error: 'No record found for the provided ICO.',
        }),
      });
    }
  });
});
