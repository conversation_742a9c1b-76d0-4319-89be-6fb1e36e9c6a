{"name": "invoice-app", "version": "1.0.0", "description": "", "main": "index.js", "directories": {"doc": "docs"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "vitest run --coverage", "test:watch": "vitest", "test:i18n": "vitest run src/test/i18n.test.ts", "test:e2e": "playwright test", "release": "semantic-release", "type-check": "npx tsc --pretty --noEmit -p .", "prepare": "husky"}, "repository": {"type": "git", "url": "git+https://github.com/borecz/invoice.git"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "bugs": {"url": "https://github.com/borecz/invoice/issues"}, "homepage": "https://github.com/borecz/invoice#readme", "dependencies": {"@supabase/supabase-js": "^2.50.1", "@tanstack/react-query": "^5.81.2", "autoprefixer": "^10.4.21", "clsx": "^2.1.1", "next": "^15.3.4", "next-intl": "^4.3.1", "next-seo": "^6.8.0", "next-sitemap": "^4.2.3", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "resend": "^4.6.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@commitlint/cli": "^19.4.1", "@commitlint/config-conventional": "^19.4.1", "@playwright/test": "^1.53.2", "@tailwindcss/postcss": "^4.1.10", "@types/node": "^24.0.4", "@types/react": "^19.1.8", "cross-env": "^7.0.3", "eslint": "^9.29.0", "eslint-config-next": "^15.3.4", "eslint-config-prettier": "^9.1.0", "husky": "^9.1.4", "lint-staged": "^15.2.7", "playwright": "^1.53.1", "prettier": "^3.6.0", "prettier-plugin-tailwindcss": "^0.6.13", "semantic-release": "^24.2.5", "tailwindcss": "^4.1.10", "typescript": "^5.8.3", "vitest": "^3.2.4"}}