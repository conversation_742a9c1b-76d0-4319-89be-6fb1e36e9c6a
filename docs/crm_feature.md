# CRM Feature - International Client Management

## Overview

The CRM feature provides comprehensive client management for international businesses with a focus on Czech and EU compliance. It supports multiple client types, country-specific tax information, and integrates with the Czech ARES business registry.

## Features

### 🌍 International Support
- **Multi-country client management** with country-specific fields
- **Czech Republic**: IČO, DIČ, Registration Number, VAT Status
- **EU Countries**: VAT Number, VAT Status
- **Non-EU Countries**: Tax ID
- **Automatic field validation** based on country selection

### 🏢 Client Types
- **Company**: Business entities with full tax compliance
- **Freelancer**: Individual professionals

### 📋 ARES Integration (Czech Republic)
- **Automatic data fetching** from Czech business registry
- **ICO validation** with checksum verification
- **Pre-fill client information** including name, address, and VAT status
- **Error handling** for invalid ICOs or service unavailability

### 👥 Contact Management
- **Multiple contacts per client** with role-based organization
- **Default contact designation** for primary communication
- **Contact information**: Name, Email, Phone, Position

### 🎨 Glass Morphism UI
- **Modern glass morphism design** with frosted glass effects
- **Liquid animations** for smooth user interactions
- **Responsive design** for desktop and mobile
- **Accessibility-focused** interface

### 🔍 Advanced Filtering & Search
- **Search by client name** with real-time results
- **Filter by country, type, VAT status**
- **Tag-based organization** for client categorization
- **Pagination** for large client lists

## Technical Implementation

### Database Schema

#### Clients Table
```sql
CREATE TABLE clients (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL REFERENCES organizations(id),
  type TEXT NOT NULL CHECK (type IN ('company', 'freelancer')),
  name TEXT NOT NULL,
  country TEXT NOT NULL DEFAULT 'CZ',
  
  -- Address information
  street TEXT,
  city TEXT NOT NULL,
  postal_code TEXT,
  address_country TEXT,
  
  -- Invoice address (optional)
  invoice_street TEXT,
  invoice_city TEXT,
  invoice_postal_code TEXT,
  invoice_country TEXT,
  
  -- Bank details
  bank_name TEXT,
  account_number TEXT,
  iban TEXT,
  swift TEXT,
  
  -- Contact information
  invoice_email TEXT,
  default_contact_id UUID,
  
  -- Additional information
  notes TEXT,
  tags TEXT[],
  
  -- Czech-specific fields
  ico VARCHAR(8) CHECK (ico IS NULL OR ico ~ '^[0-9]{8}$'),
  dic VARCHAR(12) CHECK (dic IS NULL OR dic ~ '^[A-Z]{2}[0-9]{8,10}$'),
  registration_number TEXT,
  vat_status TEXT CHECK (vat_status IS NULL OR vat_status IN ('vat_payer', 'non_vat_payer', 'unknown')),
  
  -- EU-specific fields
  vat_number TEXT,
  
  -- Non-EU specific fields
  tax_id TEXT,
  
  -- Metadata
  created_by UUID REFERENCES user_profiles(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### Client Contacts Table
```sql
CREATE TABLE client_contacts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  email TEXT,
  phone TEXT,
  position TEXT,
  is_default_contact BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### API Endpoints

#### Client Management
- `GET /api/clients` - List clients with filtering and pagination
- `POST /api/clients` - Create new client
- `GET /api/clients/:id` - Get specific client
- `PUT /api/clients/:id` - Update client
- `DELETE /api/clients/:id` - Delete client

#### ARES Integration
- `POST /api/ares` - Fetch business data from Czech ARES registry
- `GET /api/ares?ico=:ico` - Alternative GET endpoint for ARES lookup

### Frontend Components

#### Core Components
- `CRMPage` - Main CRM page container
- `ClientList` - Client listing with filters and search
- `ClientForm` - Client creation/editing form
- `ClientDetails` - Detailed client view

#### UI Components
- `GlassCard` - Glass morphism card component
- `GlassButton` - Glass morphism button component
- `GlassInput` - Glass morphism input component
- `GlassSelect` - Glass morphism select component
- `GlassModal` - Glass morphism modal component

## Usage

### Creating a Czech Client

1. Navigate to `/crm`
2. Click "Add Client"
3. Select "Company" type and "Czechia" country
4. Enter client name and IČO
5. Click "Fetch from ARES" to auto-fill data
6. Complete address and contact information
7. Submit the form

### Creating an EU Client

1. Navigate to `/crm`
2. Click "Add Client"
3. Select client type and EU country
4. Enter VAT number and VAT status
5. Complete address and contact information
6. Submit the form

### Creating a Non-EU Client

1. Navigate to `/crm`
2. Click "Add Client"
3. Select client type and non-EU country
4. Enter tax ID if applicable
5. Complete address and contact information
6. Submit the form

## Testing

### API Tests
```bash
# Run API tests
npm run test:e2e -- --project=api

# Run specific API test
npm run test:e2e -- tests/api/clients.spec.ts
```

### E2E Tests
```bash
# Run E2E tests
npm run test:e2e -- --project=chromium

# Run specific E2E test
npm run test:e2e -- tests/e2e/crm.spec.ts
```

### Test Coverage
- ✅ Client CRUD operations
- ✅ Country-specific field validation
- ✅ ARES integration
- ✅ Contact management
- ✅ Search and filtering
- ✅ Form validation
- ✅ Glass morphism UI interactions

## Configuration

### Environment Variables
```env
# Supabase configuration
SUPABASE_URL=your_supabase_url
SUPABASE_SECRET_KEY=your_supabase_secret_key
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_PUBLISHABLE_KEY=your_supabase_publishable_key

# Application URL
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### Country Configuration
Countries and their specific fields are configured in `src/lib/countries.ts`. To add support for a new country:

1. Add the country to `COUNTRY_CONFIGS`
2. Define required and optional fields
3. Add validation rules if needed
4. Update the UI to handle new fields

## Security

### Data Protection
- **Row Level Security (RLS)** ensures data isolation between organizations
- **Input validation** prevents injection attacks
- **Authentication required** for all API endpoints
- **CORS protection** for API endpoints

### Validation
- **Server-side validation** for all client data
- **Country-specific validation** for tax numbers
- **Email and phone format validation**
- **IBAN validation** for bank details

## Performance

### Optimizations
- **Database indexes** on frequently queried fields
- **Pagination** for large client lists
- **Debounced search** to reduce API calls
- **Lazy loading** for client details

### Caching
- **Client-side caching** of country configurations
- **Optimistic updates** for better UX
- **Background refresh** of client statistics

## Future Enhancements

### Planned Features
- **Import/Export** functionality for client data
- **Advanced reporting** and analytics
- **Integration with accounting systems**
- **Mobile app** for client management
- **Bulk operations** for client management
- **Client portal** for self-service updates

### Technical Improvements
- **Real-time updates** using Supabase subscriptions
- **Offline support** with service workers
- **Advanced search** with full-text search
- **Audit logging** for compliance
- **API rate limiting** for ARES integration
