// CRM Types for International Client Management
// Comprehensive type definitions for the invoice app CRM system

// Client Types
export type ClientType = 'company' | 'freelancer';

// VAT Status Types
export type VATStatus = 'vat_payer' | 'non_vat_payer' | 'unknown';

// Country Codes (ISO 3166-1 alpha-2)
export type CountryCode =
  | 'CZ' // Czechia
  | 'SK' // Slovakia
  | 'DE' // Germany
  | 'AT' // Austria
  | 'PL' // Poland
  | 'HU' // Hungary
  | 'SI' // Slovenia
  | 'HR' // Croatia
  | 'RO' // Romania
  | 'BG' // Bulgaria
  | 'FR' // France
  | 'IT' // Italy
  | 'ES' // Spain
  | 'PT' // Portugal
  | 'NL' // Netherlands
  | 'BE' // Belgium
  | 'LU' // Luxembourg
  | 'DK' // Denmark
  | 'SE' // Sweden
  | 'FI' // Finland
  | 'EE' // Estonia
  | 'LV' // Latvia
  | 'LT' // Lithuania
  | 'IE' // Ireland
  | 'MT' // Malta
  | 'CY' // Cyprus
  | 'GR' // Greece
  | 'US' // United States
  | 'CA' // Canada
  | 'GB' // United Kingdom
  | 'CH' // Switzerland
  | 'NO' // Norway
  | 'IS' // Iceland
  | 'JP' // Japan
  | 'AU' // Australia
  | 'NZ' // New Zealand
  | string; // Allow other countries

// Address Interface
export interface Address {
  street?: string;
  city: string;
  postal_code?: string;
  country: CountryCode;
}

// Bank Details Interface
export interface BankDetails {
  bank_name?: string;
  account_number?: string;
  iban?: string;
  swift?: string;
}

// Client Contact Interface
export interface ClientContact {
  id: string;
  client_id: string;
  name: string;
  email?: string;
  phone?: string;
  position?: string;
  is_default_contact: boolean;
  created_at: string;
  updated_at: string;
}

// Client Country Specific Fields Interface
export interface ClientCountrySpecific {
  id: string;
  client_id: string;
  country_code: CountryCode;
  field_name: string;
  field_value?: string;
  created_at: string;
  updated_at: string;
}

// Main Client Interface
export interface Client {
  id: string;
  organization_id: string;

  // Core information
  type: ClientType;
  name: string;
  country: CountryCode;

  // Address information
  street?: string;
  city: string;
  postal_code?: string;
  address_country?: CountryCode; // mirrors country field

  // Invoice address (optional)
  invoice_street?: string;
  invoice_city?: string;
  invoice_postal_code?: string;
  invoice_country?: CountryCode;

  // Bank details
  bank_name?: string;
  account_number?: string;
  iban?: string;
  swift?: string;

  // Contact information
  invoice_email?: string;
  default_contact_id?: string;

  // Additional information
  notes?: string;
  tags?: string[];

  // Czech-specific fields
  ico?: string; // 8 digits
  dic?: string; // CZ + 8-10 digits
  registration_number?: string;
  vat_status?: VATStatus;

  // EU-specific fields
  vat_number?: string;

  // Non-EU specific fields
  tax_id?: string;

  // Metadata
  created_by?: string;
  created_at: string;
  updated_at: string;
}

// Client with Relations Interface
export interface ClientWithRelations extends Client {
  contacts: ClientContact[];
  country_specifics: ClientCountrySpecific[];
  default_contact?: ClientContact;
}

// Client Form Data Interface
export interface ClientFormData {
  // Core information
  type: ClientType;
  name: string;
  country: CountryCode;

  // Address information
  street?: string;
  city: string;
  postal_code?: string;

  // Invoice address
  use_same_address: boolean;
  invoice_street?: string;
  invoice_city?: string;
  invoice_postal_code?: string;
  invoice_country?: CountryCode;

  // Bank details
  bank_name?: string;
  account_number?: string;
  iban?: string;
  swift?: string;

  // Contact information
  invoice_email?: string;

  // Additional information
  notes?: string;
  tags?: string[];

  // Country-specific fields (dynamic based on country)
  ico?: string;
  dic?: string;
  registration_number?: string;
  vat_status?: VATStatus;
  vat_number?: string;
  tax_id?: string;

  // Contacts
  contacts: Omit<
    ClientContact,
    'id' | 'client_id' | 'created_at' | 'updated_at'
  >[];
}

// ARES API Response Interface (Czech business registry)
export interface ARESResponse {
  success: boolean;
  data?: {
    ico: string;
    name: string;
    dic?: string;
    address: {
      street: string;
      city: string;
      postal_code: string;
      country: string;
    };
    vat_status: VATStatus;
    registration_number?: string;
  };
  error?: string;
}

// Country Configuration Interface
export interface CountryConfig {
  code: CountryCode;
  name: string;
  is_eu: boolean;
  currency: string;
  vat_number_format?: RegExp;
  required_fields: string[];
  optional_fields: string[];
}

// Client List Filters Interface
export interface ClientFilters {
  search?: string;
  country?: CountryCode;
  type?: ClientType;
  vat_status?: VATStatus;
  tags?: string[];
  page?: number;
  limit?: number;
  sort_by?: 'name' | 'country' | 'created_at' | 'updated_at';
  sort_order?: 'asc' | 'desc';
}

// Client Statistics Interface
export interface ClientStats {
  total_clients: number;
  by_country: Record<CountryCode, number>;
  by_type: Record<ClientType, number>;
  by_vat_status: Record<VATStatus, number>;
  recent_clients: Client[];
}

// API Response Types
export interface CRMResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
}

export interface ClientListResponse extends CRMResponse {
  data: {
    clients: ClientWithRelations[];
    stats: ClientStats;
  };
}

export interface ClientResponse extends CRMResponse {
  data: ClientWithRelations;
}

// Form Validation Schemas
export interface ClientValidationErrors {
  name?: string;
  country?: string;
  city?: string;
  type?: string;
  ico?: string;
  dic?: string;
  vat_number?: string;
  iban?: string;
  email?: string;
  contacts?: Array<{
    name?: string;
    email?: string;
    phone?: string;
  }>;
}

// UI State Types
export interface ClientFormState {
  data: ClientFormData;
  errors: ClientValidationErrors;
  loading: boolean;
  submitting: boolean;
  ares_loading: boolean;
}

export interface ClientListState {
  clients: ClientWithRelations[];
  filters: ClientFilters;
  stats: ClientStats;
  loading: boolean;
  error?: string;
}
