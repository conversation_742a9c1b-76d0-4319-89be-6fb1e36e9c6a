// ARES API Integration for Czech Business Registry
// Fetches business information from the Czech ARES database

import { ARESResponse, VATStatus } from '@/types/crm';

// ARES API Configuration
const ARES_BASE_URL = 'https://wwwinfo.mfcr.cz/cgi-bin/ares/darv_bas.cgi';
const ARES_TIMEOUT = 10000; // 10 seconds

// ARES XML Response Interface
interface ARESXMLResponse {
  'are:Ares_odpovedi': {
    'are:Odpoved': {
      'are:Pocet_zaznamu': string;
      'are:Typ_vyhledani': string;
      'are:Zaznam'?: {
        'are:Identifikace': {
          'are:ICO': string;
        };
        'are:Obchodni_firma': string;
        'are:Pravni_forma': {
          'are:Kod_PF': string;
          'are:Nazev_PF': string;
        };
        'are:Adresa_ARES': {
          'are:ID_adresy': string;
          'are:Kod_statu': string;
          'are:Nazev_okresu': string;
          'are:Nazev_obce': string;
          'are:Nazev_casti_obce'?: string;
          'are:Nazev_ulice'?: string;
          'are:Cislo_domovni'?: string;
          'are:Typ_cislo_domovni'?: string;
          'are:Cislo_orientacni'?: string;
          'are:Znak_cisla_orientacniho'?: string;
          'are:PSC': string;
          'are:Adresa_UIR': {
            'are:Kod_ADM': string;
            'are:Kod_obce': string;
            'are:Kod_casti_obce'?: string;
            'are:Kod_ulice'?: string;
          };
        };
        'are:Datum_vzniku': string;
        'are:Datum_zaniku'?: string;
        'are:Pravni_stav': string;
        'are:Financni_urad': string;
        'are:Datum_posledni_zmeny': string;
        'are:Priznaky_subjektu': string;
      };
      'are:Error'?: {
        'are:Error_kod': string;
        'are:Error_text': string;
      };
    };
  };
}

// Utility function to parse XML response
function parseXMLResponse(xmlText: string): any {
  // This is a simplified XML parser for ARES responses
  // In a production environment, you might want to use a proper XML parser
  try {
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(xmlText, 'text/xml');

    // Check for parsing errors
    const parserError = xmlDoc.querySelector('parsererror');
    if (parserError) {
      throw new Error('XML parsing error');
    }

    return xmlDoc;
  } catch (error) {
    throw new Error('Failed to parse XML response');
  }
}

// Extract text content from XML element
function getXMLTextContent(xmlDoc: Document, selector: string): string | null {
  const element = xmlDoc.querySelector(selector);
  return element?.textContent?.trim() || null;
}

// Format address from ARES data
function formatAddress(xmlDoc: Document): {
  street: string;
  city: string;
  postal_code: string;
  country: string;
} {
  const street_name = getXMLTextContent(xmlDoc, 'Nazev_ulice');
  const house_number = getXMLTextContent(xmlDoc, 'Cislo_domovni');
  const orientation_number = getXMLTextContent(xmlDoc, 'Cislo_orientacni');
  const city = getXMLTextContent(xmlDoc, 'Nazev_obce') || '';
  const postal_code = getXMLTextContent(xmlDoc, 'PSC') || '';

  // Build street address
  let street = '';
  if (street_name) {
    street = street_name;
    if (house_number) {
      street += ` ${house_number}`;
      if (orientation_number) {
        street += `/${orientation_number}`;
      }
    }
  } else if (house_number) {
    street = house_number;
    if (orientation_number) {
      street += `/${orientation_number}`;
    }
  }

  return {
    street,
    city,
    postal_code,
    country: 'CZ',
  };
}

// Determine VAT status from ARES data
function determineVATStatus(xmlDoc: Document): VATStatus {
  // This is a simplified determination
  // In reality, you might need to check additional ARES endpoints for VAT registration
  const legal_form_code = getXMLTextContent(xmlDoc, 'Kod_PF');
  const status = getXMLTextContent(xmlDoc, 'Pravni_stav');

  // If the entity is active and is a business entity, assume it might be VAT registered
  if (status === 'A' && legal_form_code) {
    // Common business legal forms that are typically VAT registered
    const businessForms = ['112', '113', '117', '121', '205', '601', '701'];
    if (businessForms.includes(legal_form_code)) {
      return 'unknown'; // We can't determine for sure without additional API calls
    }
  }

  return 'unknown';
}

// Main ARES API function
export async function fetchFromARES(ico: string): Promise<ARESResponse> {
  // Validate ICO format
  if (!ico || !/^[0-9]{8}$/.test(ico)) {
    return {
      success: false,
      error: 'Invalid ICO format. ICO must be 8 digits.',
    };
  }

  try {
    // Build ARES API URL
    const url = `${ARES_BASE_URL}?ico=${ico}`;

    // Fetch data from ARES
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), ARES_TIMEOUT);

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        Accept: 'application/xml, text/xml',
        'User-Agent': 'Invoice-Hub-CRM/1.0',
      },
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(
        `ARES API returned ${response.status}: ${response.statusText}`,
      );
    }

    const xmlText = await response.text();
    const xmlDoc = parseXMLResponse(xmlText);

    // Check for ARES errors
    const errorCode = getXMLTextContent(xmlDoc, 'Error_kod');
    if (errorCode) {
      const errorText =
        getXMLTextContent(xmlDoc, 'Error_text') || 'Unknown ARES error';
      return {
        success: false,
        error: `ARES Error ${errorCode}: ${errorText}`,
      };
    }

    // Check if record was found
    const recordCount = getXMLTextContent(xmlDoc, 'Pocet_zaznamu');
    if (recordCount === '0') {
      return {
        success: false,
        error: 'No record found for the provided ICO.',
      };
    }

    // Extract business information
    const name = getXMLTextContent(xmlDoc, 'Obchodni_firma');
    if (!name) {
      return {
        success: false,
        error: 'Unable to extract business name from ARES response.',
      };
    }

    const address = formatAddress(xmlDoc);
    const vatStatus = determineVATStatus(xmlDoc);

    // Try to extract DIC if available (this might require additional API calls)
    // For now, we'll leave it empty as basic ARES doesn't always include DIC
    const dic = undefined;

    return {
      success: true,
      data: {
        ico,
        name,
        dic,
        address,
        vat_status: vatStatus,
        registration_number: ico, // ICO serves as registration number in Czech Republic
      },
    };
  } catch (error) {
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        return {
          success: false,
          error:
            'Request timeout. ARES service might be temporarily unavailable.',
        };
      }

      return {
        success: false,
        error: `Failed to fetch data from ARES: ${error.message}`,
      };
    }

    return {
      success: false,
      error: 'An unexpected error occurred while fetching data from ARES.',
    };
  }
}

// Validate ICO using checksum algorithm
export function validateICOChecksum(ico: string): boolean {
  if (!ico || ico.length !== 8 || !/^[0-9]{8}$/.test(ico)) {
    return false;
  }

  const digits = ico.split('').map(Number);
  const weights = [8, 7, 6, 5, 4, 3, 2];

  let sum = 0;
  for (let i = 0; i < 7; i++) {
    sum += digits[i] * weights[i];
  }

  const remainder = sum % 11;
  let checkDigit: number;

  if (remainder < 2) {
    checkDigit = remainder;
  } else {
    checkDigit = 11 - remainder;
  }

  return digits[7] === checkDigit;
}

// Format ICO for display
export function formatICO(ico: string): string {
  if (!ico || ico.length !== 8) return ico;
  return `${ico.slice(0, 2)} ${ico.slice(2, 5)} ${ico.slice(5)}`;
}

// Format DIC for display
export function formatDIC(dic: string): string {
  if (!dic || !dic.startsWith('CZ')) return dic;
  const numbers = dic.slice(2);
  if (numbers.length === 8) {
    return `CZ ${numbers.slice(0, 2)} ${numbers.slice(2, 5)} ${numbers.slice(5)}`;
  } else if (numbers.length === 9) {
    return `CZ ${numbers.slice(0, 3)} ${numbers.slice(3, 6)} ${numbers.slice(6)}`;
  } else if (numbers.length === 10) {
    return `CZ ${numbers.slice(0, 4)} ${numbers.slice(4, 7)} ${numbers.slice(7)}`;
  }
  return dic;
}
