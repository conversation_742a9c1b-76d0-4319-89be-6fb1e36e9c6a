import { createClient } from '@supabase/supabase-js';

// For client-side, we need to use NEXT_PUBLIC_ prefixed variables
const supabaseUrl =
  process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseAnonKey =
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ||
  process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error(
    'Missing required Supabase environment variables. Please set either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY (for client-side) or SUPABASE_URL and SUPABASE_ANON_KEY (for server-side).',
  );
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Auth helpers
export const auth = {
  signUp: async (email: string, password: string, metadata?: any) => {
    return await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata,
      },
    });
  },

  signIn: async (email: string, password: string) => {
    return await supabase.auth.signInWithPassword({
      email,
      password,
    });
  },

  signOut: async () => {
    return await supabase.auth.signOut();
  },

  getSession: async () => {
    return await supabase.auth.getSession();
  },

  getUser: async () => {
    return await supabase.auth.getUser();
  },

  resetPassword: async (email: string, redirectTo?: string) => {
    return await supabase.auth.resetPasswordForEmail(email, {
      redirectTo,
    });
  },

  updatePassword: async (password: string) => {
    return await supabase.auth.updateUser({
      password,
    });
  },

  resendConfirmation: async (email: string) => {
    return await supabase.auth.resend({
      type: 'signup',
      email,
    });
  },
};

// Database helpers
export const db = {
  // User profiles
  getUserProfile: async (userId: string) => {
    return await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', userId)
      .single();
  },

  updateUserProfile: async (userId: string, updates: any) => {
    return await supabase
      .from('user_profiles')
      .update(updates)
      .eq('id', userId);
  },

  // Organizations
  getUserOrganizations: async (userId: string) => {
    return await supabase
      .from('user_organizations')
      .select(
        `
        role,
        joined_at,
        organization:organizations(*)
      `,
      )
      .eq('user_id', userId);
  },

  getOrganization: async (organizationId: string) => {
    return await supabase
      .from('organizations')
      .select('*')
      .eq('id', organizationId)
      .single();
  },

  updateOrganization: async (organizationId: string, updates: any) => {
    return await supabase
      .from('organizations')
      .update(updates)
      .eq('id', organizationId);
  },
};

export default supabase;
