// Country Configuration and Validation Utilities
// Comprehensive country support for international CRM

import { CountryCode, CountryConfig, VATStatus } from '@/types/crm';

// EU Country Codes
export const EU_COUNTRIES: CountryCode[] = [
  'AT',
  'BE',
  'BG',
  'HR',
  'CY',
  'CZ',
  'DK',
  'EE',
  'FI',
  'FR',
  'DE',
  'GR',
  'HU',
  'IE',
  'IT',
  'LV',
  'LT',
  'LU',
  'MT',
  'NL',
  'PL',
  'PT',
  'RO',
  'SK',
  'SI',
  'ES',
  'SE',
];

// Country Configurations
export const COUNTRY_CONFIGS: Record<CountryCode, CountryConfig> = {
  // Czechia (Primary focus)
  CZ: {
    code: 'CZ',
    name: 'Czechia',
    is_eu: true,
    currency: 'CZK',
    vat_number_format: /^CZ[0-9]{8,10}$/,
    required_fields: ['name', 'city', 'country'],
    optional_fields: ['ico', 'dic', 'registration_number', 'vat_status'],
  },

  // Other EU Countries
  SK: {
    code: 'SK',
    name: 'Slovakia',
    is_eu: true,
    currency: 'EUR',
    vat_number_format: /^SK[0-9]{10}$/,
    required_fields: ['name', 'city', 'country'],
    optional_fields: ['vat_number', 'vat_status'],
  },
  DE: {
    code: 'DE',
    name: 'Germany',
    is_eu: true,
    currency: 'EUR',
    vat_number_format: /^DE[0-9]{9}$/,
    required_fields: ['name', 'city', 'country'],
    optional_fields: ['vat_number', 'vat_status'],
  },
  AT: {
    code: 'AT',
    name: 'Austria',
    is_eu: true,
    currency: 'EUR',
    vat_number_format: /^ATU[0-9]{8}$/,
    required_fields: ['name', 'city', 'country'],
    optional_fields: ['vat_number', 'vat_status'],
  },
  PL: {
    code: 'PL',
    name: 'Poland',
    is_eu: true,
    currency: 'PLN',
    vat_number_format: /^PL[0-9]{10}$/,
    required_fields: ['name', 'city', 'country'],
    optional_fields: ['vat_number', 'vat_status'],
  },
  HU: {
    code: 'HU',
    name: 'Hungary',
    is_eu: true,
    currency: 'HUF',
    vat_number_format: /^HU[0-9]{8}$/,
    required_fields: ['name', 'city', 'country'],
    optional_fields: ['vat_number', 'vat_status'],
  },
  FR: {
    code: 'FR',
    name: 'France',
    is_eu: true,
    currency: 'EUR',
    vat_number_format: /^FR[0-9A-Z]{2}[0-9]{9}$/,
    required_fields: ['name', 'city', 'country'],
    optional_fields: ['vat_number', 'vat_status'],
  },
  IT: {
    code: 'IT',
    name: 'Italy',
    is_eu: true,
    currency: 'EUR',
    vat_number_format: /^IT[0-9]{11}$/,
    required_fields: ['name', 'city', 'country'],
    optional_fields: ['vat_number', 'vat_status'],
  },
  ES: {
    code: 'ES',
    name: 'Spain',
    is_eu: true,
    currency: 'EUR',
    vat_number_format: /^ES[0-9A-Z][0-9]{7}[0-9A-Z]$/,
    required_fields: ['name', 'city', 'country'],
    optional_fields: ['vat_number', 'vat_status'],
  },
  NL: {
    code: 'NL',
    name: 'Netherlands',
    is_eu: true,
    currency: 'EUR',
    vat_number_format: /^NL[0-9]{9}B[0-9]{2}$/,
    required_fields: ['name', 'city', 'country'],
    optional_fields: ['vat_number', 'vat_status'],
  },
  BE: {
    code: 'BE',
    name: 'Belgium',
    is_eu: true,
    currency: 'EUR',
    vat_number_format: /^BE[0-9]{10}$/,
    required_fields: ['name', 'city', 'country'],
    optional_fields: ['vat_number', 'vat_status'],
  },

  // Non-EU Countries
  US: {
    code: 'US',
    name: 'United States',
    is_eu: false,
    currency: 'USD',
    required_fields: ['name', 'city', 'country'],
    optional_fields: ['tax_id'],
  },
  CA: {
    code: 'CA',
    name: 'Canada',
    is_eu: false,
    currency: 'CAD',
    required_fields: ['name', 'city', 'country'],
    optional_fields: ['tax_id'],
  },
  GB: {
    code: 'GB',
    name: 'United Kingdom',
    is_eu: false,
    currency: 'GBP',
    vat_number_format: /^GB[0-9]{9}$/,
    required_fields: ['name', 'city', 'country'],
    optional_fields: ['vat_number', 'tax_id'],
  },
  CH: {
    code: 'CH',
    name: 'Switzerland',
    is_eu: false,
    currency: 'CHF',
    vat_number_format: /^CHE-[0-9]{9}$/,
    required_fields: ['name', 'city', 'country'],
    optional_fields: ['vat_number', 'tax_id'],
  },
  NO: {
    code: 'NO',
    name: 'Norway',
    is_eu: false,
    currency: 'NOK',
    vat_number_format: /^NO[0-9]{9}MVA$/,
    required_fields: ['name', 'city', 'country'],
    optional_fields: ['vat_number', 'tax_id'],
  },
  JP: {
    code: 'JP',
    name: 'Japan',
    is_eu: false,
    currency: 'JPY',
    required_fields: ['name', 'city', 'country'],
    optional_fields: ['tax_id'],
  },
  AU: {
    code: 'AU',
    name: 'Australia',
    is_eu: false,
    currency: 'AUD',
    required_fields: ['name', 'city', 'country'],
    optional_fields: ['tax_id'],
  },
};

// Utility Functions
export function isEUCountry(countryCode: CountryCode): boolean {
  return EU_COUNTRIES.includes(countryCode);
}

export function getCountryConfig(
  countryCode: CountryCode,
): CountryConfig | null {
  return COUNTRY_CONFIGS[countryCode] || null;
}

export function getCountryName(countryCode: CountryCode): string {
  const config = getCountryConfig(countryCode);
  return config?.name || countryCode;
}

export function getCountryCurrency(countryCode: CountryCode): string {
  const config = getCountryConfig(countryCode);
  return config?.currency || 'EUR';
}

export function getRequiredFields(countryCode: CountryCode): string[] {
  const config = getCountryConfig(countryCode);
  return config?.required_fields || ['name', 'city', 'country'];
}

export function getOptionalFields(countryCode: CountryCode): string[] {
  const config = getCountryConfig(countryCode);
  return config?.optional_fields || [];
}

export function getCountrySpecificFields(countryCode: CountryCode): string[] {
  if (countryCode === 'CZ') {
    return ['ico', 'dic', 'registration_number', 'vat_status'];
  } else if (isEUCountry(countryCode)) {
    return ['vat_number', 'vat_status'];
  } else {
    return ['tax_id'];
  }
}

// Validation Functions
export function validateICO(ico: string): boolean {
  if (!ico || ico.length !== 8) return false;
  return /^[0-9]{8}$/.test(ico);
}

export function validateDIC(dic: string): boolean {
  if (!dic) return false;
  return /^CZ[0-9]{8,10}$/.test(dic);
}

export function validateVATNumber(
  vatNumber: string,
  countryCode: CountryCode,
): boolean {
  if (!vatNumber) return false;

  const config = getCountryConfig(countryCode);
  if (!config?.vat_number_format) return true; // No specific format required

  return config.vat_number_format.test(vatNumber);
}

export function validateIBAN(iban: string): boolean {
  if (!iban) return true; // IBAN is optional

  // Remove spaces and convert to uppercase
  const cleanIban = iban.replace(/\s/g, '').toUpperCase();

  // Basic IBAN format check (2 letters + 2 digits + up to 30 alphanumeric)
  if (!/^[A-Z]{2}[0-9]{2}[A-Z0-9]{1,30}$/.test(cleanIban)) {
    return false;
  }

  // IBAN checksum validation (simplified)
  const rearranged = cleanIban.slice(4) + cleanIban.slice(0, 4);
  const numericString = rearranged.replace(/[A-Z]/g, (char) =>
    (char.charCodeAt(0) - 55).toString(),
  );

  // Calculate mod 97
  let remainder = 0;
  for (let i = 0; i < numericString.length; i++) {
    remainder = (remainder * 10 + parseInt(numericString[i])) % 97;
  }

  return remainder === 1;
}

export function validateEmail(email: string): boolean {
  if (!email) return true; // Email is optional

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function validatePhone(phone: string): boolean {
  if (!phone) return true; // Phone is optional

  // Basic phone validation (allows various formats)
  const phoneRegex = /^[\+]?[0-9\s\-\(\)]{7,20}$/;
  return phoneRegex.test(phone);
}

// Country List for Dropdowns
export const COUNTRY_OPTIONS = Object.values(COUNTRY_CONFIGS)
  .sort((a, b) => {
    // Czechia first, then alphabetical
    if (a.code === 'CZ') return -1;
    if (b.code === 'CZ') return 1;
    return a.name.localeCompare(b.name);
  })
  .map((config) => ({
    value: config.code,
    label: config.name,
    currency: config.currency,
    is_eu: config.is_eu,
  }));

// VAT Status Options
export const VAT_STATUS_OPTIONS = [
  { value: 'vat_payer', label: 'VAT Payer' },
  { value: 'non_vat_payer', label: 'Non-VAT Payer' },
  { value: 'unknown', label: 'Unknown' },
];

// Client Type Options
export const CLIENT_TYPE_OPTIONS = [
  { value: 'company', label: 'Company' },
  { value: 'freelancer', label: 'Freelancer' },
];
