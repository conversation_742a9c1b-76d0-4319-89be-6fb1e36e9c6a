import { Resend } from 'resend';

// Make Resend optional during build time
const resend = process.env.RESEND_API_KEY
  ? new Resend(process.env.RESEND_API_KEY)
  : null;

export async function sendWelcomeEmail(
  email: string,
  name: string,
  verificationUrl: string,
) {
  if (!resend) {
    console.warn('Resend API key not configured, email sending disabled');
    return { id: 'mock-email-id' };
  }

  try {
    const { data, error } = await resend.emails.send({
      from: 'Invoice Hub <<EMAIL>>',
      to: [email],
      subject: 'Welcome to Invoice Hub - Verify Your Email',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1>Welcome to Invoice Hub, ${name}!</h1>
          <p>Thank you for signing up. Please verify your email address by clicking the link below:</p>
          <a href="${verificationUrl}" style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
            Verify Email Address
          </a>
          <p>If you didn't create an account, you can safely ignore this email.</p>
          <p>Best regards,<br>The Invoice Hub Team</p>
        </div>
      `,
    });

    if (error) {
      console.error('Email sending error:', error);
      throw new Error(`Failed to send email: ${error.message}`);
    }

    return data;
  } catch (error) {
    console.error('Email service error:', error);
    throw error;
  }
}
