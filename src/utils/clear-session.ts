/**
 * Utility to clear corrupted session data
 * This can be used when there are authentication issues like invalid refresh tokens
 */

import { authStorage } from '@/lib/auth-storage';
import { supabase } from '@/lib/supabase';

export async function clearCorruptedSession() {
  try {
    console.log('Clearing corrupted session data...');
    
    // Clear secure session storage
    await authStorage.clear();
    
    // Sign out from Supabase
    await supabase.auth.signOut();
    
    // Clear any remaining session storage items
    if (typeof window !== 'undefined') {
      sessionStorage.clear();
      
      // Also clear any localStorage items that might be related to auth
      const authKeys = Object.keys(localStorage).filter(key => 
        key.includes('auth') || 
        key.includes('session') || 
        key.includes('supabase') ||
        key.includes('access_token')
      );
      
      authKeys.forEach(key => {
        localStorage.removeItem(key);
        console.log(`Removed localStorage key: ${key}`);
      });
    }
    
    console.log('Session cleared successfully. Please refresh the page and login again.');
    return true;
  } catch (error) {
    console.error('Error clearing session:', error);
    return false;
  }
}

// Make it available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).clearCorruptedSession = clearCorruptedSession;
}
