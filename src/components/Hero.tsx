'use client';

import Link from 'next/link';
import { useTranslations, useLocale } from 'next-intl';

export default function Hero() {
  const t = useTranslations('Landing.Hero');
  const locale = useLocale();
  return (
    <section className="relative w-full bg-gradient-to-br from-gray-50 to-white py-20 md:py-32 lg:py-40">
      <div className="container mx-auto px-4 md:px-6">
        <div className="mx-auto max-w-4xl text-center">
          <div className="mb-8 inline-flex items-center rounded-full border border-gray-200 bg-white px-4 py-2 text-sm text-gray-600 shadow-sm">
            <span className="mr-2 h-2 w-2 rounded-full bg-green-500"></span>
            {t('badge')}
          </div>

          <h1 className="mb-6 text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl md:text-6xl lg:text-7xl">
            {t('title')}
          </h1>

          <p className="mx-auto mb-8 max-w-2xl text-lg text-gray-600 md:text-xl lg:text-2xl">
            {t('subtitle')}
          </p>

          <div className="flex flex-col items-center justify-center gap-4 sm:flex-row">
            <Link
              href={`/${locale}/get-started`}
              className="inline-flex h-12 items-center justify-center rounded-lg bg-gray-900 px-8 text-base font-medium text-white shadow-lg transition-all hover:bg-gray-800 hover:shadow-xl focus-visible:ring-2 focus-visible:ring-gray-950 focus-visible:ring-offset-2 focus-visible:outline-none"
            >
              {t('startTrial')}
            </Link>
            <Link
              href={`/${locale}/demo`}
              className="inline-flex h-12 items-center justify-center rounded-lg border border-gray-300 bg-white px-8 text-base font-medium text-gray-700 shadow-sm transition-all hover:bg-gray-50 hover:shadow-md focus-visible:ring-2 focus-visible:ring-gray-950 focus-visible:ring-offset-2 focus-visible:outline-none"
            >
              <svg
                className="mr-2 h-4 w-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                />
              </svg>
              {t('seeDemo')}
            </Link>
          </div>

          <div className="mt-16">
            <div className="mx-auto max-w-4xl">
              <div className="relative rounded-xl bg-white p-2 shadow-2xl ring-1 ring-gray-900/10">
                <div className="rounded-lg bg-gray-900 p-8">
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                    <div className="rounded-lg bg-gray-800 p-4">
                      <div className="mb-2 h-4 w-3/4 rounded bg-gray-600"></div>
                      <div className="h-3 w-1/2 rounded bg-gray-700"></div>
                    </div>
                    <div className="rounded-lg bg-gray-800 p-4">
                      <div className="mb-2 h-4 w-2/3 rounded bg-gray-600"></div>
                      <div className="h-3 w-3/4 rounded bg-gray-700"></div>
                    </div>
                    <div className="rounded-lg bg-gray-800 p-4">
                      <div className="mb-2 h-4 w-1/2 rounded bg-gray-600"></div>
                      <div className="h-3 w-2/3 rounded bg-gray-700"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute -top-40 -right-32 h-80 w-80 rounded-full bg-gradient-to-br from-blue-400 to-purple-600 opacity-20 blur-3xl"></div>
        <div className="absolute -bottom-40 -left-32 h-80 w-80 rounded-full bg-gradient-to-br from-pink-400 to-red-600 opacity-20 blur-3xl"></div>
      </div>
    </section>
  );
}
