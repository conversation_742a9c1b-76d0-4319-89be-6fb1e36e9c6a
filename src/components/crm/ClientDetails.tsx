'use client';

import React from 'react';
import { ClientWithRelations } from '@/types/crm';
import { GlassCard, GlassButton, GlassBadge } from '@/components/ui/glass';
import { getCountryName } from '@/lib/countries';
import { formatICO, formatDIC } from '@/lib/ares';

interface ClientDetailsProps {
  client: ClientWithRelations;
  onEdit: () => void;
  onDelete: () => void;
  onBack: () => void;
}

export function ClientDetails({
  client,
  onEdit,
  onDelete,
  onBack,
}: ClientDetailsProps) {
  // Get VAT status badge variant
  const getVATStatusVariant = (status?: string) => {
    switch (status) {
      case 'vat_payer':
        return 'success';
      case 'non_vat_payer':
        return 'warning';
      default:
        return 'default';
    }
  };

  // Format address
  const formatAddress = (
    street?: string,
    city?: string,
    postalCode?: string,
    country?: string,
  ) => {
    const parts = [street, city, postalCode].filter(Boolean);
    const address = parts.join(', ');
    return country && address
      ? `${address}, ${getCountryName(country)}`
      : address;
  };

  // Get default contact
  const defaultContact =
    client.contacts?.find((c) => c.is_default_contact) || client.contacts?.[0];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <GlassButton variant="ghost" size="sm" onClick={onBack}>
            ← Back to List
          </GlassButton>
          <div>
            <h2 className="text-2xl font-bold text-white">{client.name}</h2>
            <div className="mt-1 flex items-center gap-2">
              <GlassBadge variant="info" size="sm">
                {getCountryName(client.country)}
              </GlassBadge>
              <GlassBadge variant="default" size="sm">
                {client.type}
              </GlassBadge>
              {client.vat_status && (
                <GlassBadge
                  variant={getVATStatusVariant(client.vat_status)}
                  size="sm"
                >
                  {client.vat_status.replace('_', ' ')}
                </GlassBadge>
              )}
            </div>
          </div>
        </div>

        <div className="flex gap-2">
          <GlassButton variant="secondary" onClick={onEdit}>
            Edit Client
          </GlassButton>
          <GlassButton variant="ghost" onClick={onDelete}>
            Delete
          </GlassButton>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Basic Information */}
        <GlassCard className="p-6">
          <h3 className="mb-4 text-lg font-semibold text-white">
            Basic Information
          </h3>

          <div className="space-y-3 text-white/80">
            <div>
              <span className="font-medium text-white">Type:</span>{' '}
              {client.type}
            </div>
            <div>
              <span className="font-medium text-white">Country:</span>{' '}
              {getCountryName(client.country)}
            </div>
            {client.invoice_email && (
              <div>
                <span className="font-medium text-white">Invoice Email:</span>{' '}
                {client.invoice_email}
              </div>
            )}
            {client.notes && (
              <div>
                <span className="font-medium text-white">Notes:</span>
                <div className="mt-1 rounded-lg bg-white/5 p-3">
                  {client.notes}
                </div>
              </div>
            )}
            {client.tags && client.tags.length > 0 && (
              <div>
                <span className="font-medium text-white">Tags:</span>
                <div className="mt-1 flex flex-wrap gap-1">
                  {client.tags.map((tag, index) => (
                    <GlassBadge key={index} variant="default" size="sm">
                      {tag}
                    </GlassBadge>
                  ))}
                </div>
              </div>
            )}
          </div>
        </GlassCard>

        {/* Tax Information */}
        {(client.ico ||
          client.dic ||
          client.vat_number ||
          client.tax_id ||
          client.registration_number) && (
          <GlassCard className="p-6">
            <h3 className="mb-4 text-lg font-semibold text-white">
              Tax Information
            </h3>

            <div className="space-y-3 text-white/80">
              {client.ico && (
                <div>
                  <span className="font-medium text-white">IČO:</span>{' '}
                  {formatICO(client.ico)}
                </div>
              )}
              {client.dic && (
                <div>
                  <span className="font-medium text-white">DIČ:</span>{' '}
                  {formatDIC(client.dic)}
                </div>
              )}
              {client.vat_number && (
                <div>
                  <span className="font-medium text-white">VAT Number:</span>{' '}
                  {client.vat_number}
                </div>
              )}
              {client.tax_id && (
                <div>
                  <span className="font-medium text-white">Tax ID:</span>{' '}
                  {client.tax_id}
                </div>
              )}
              {client.registration_number && (
                <div>
                  <span className="font-medium text-white">
                    Registration Number:
                  </span>{' '}
                  {client.registration_number}
                </div>
              )}
              {client.vat_status && (
                <div>
                  <span className="font-medium text-white">VAT Status:</span>{' '}
                  {client.vat_status.replace('_', ' ')}
                </div>
              )}
            </div>
          </GlassCard>
        )}

        {/* Address Information */}
        <GlassCard className="p-6">
          <h3 className="mb-4 text-lg font-semibold text-white">
            Address Information
          </h3>

          <div className="space-y-4">
            <div>
              <h4 className="mb-2 font-medium text-white">Main Address</h4>
              <div className="text-white/80">
                {formatAddress(
                  client.street,
                  client.city,
                  client.postal_code,
                  client.country,
                ) || 'No address provided'}
              </div>
            </div>

            {(client.invoice_street || client.invoice_city) && (
              <div>
                <h4 className="mb-2 font-medium text-white">Invoice Address</h4>
                <div className="text-white/80">
                  {formatAddress(
                    client.invoice_street,
                    client.invoice_city,
                    client.invoice_postal_code,
                    client.invoice_country,
                  )}
                </div>
              </div>
            )}
          </div>
        </GlassCard>

        {/* Bank Details */}
        {(client.bank_name ||
          client.account_number ||
          client.iban ||
          client.swift) && (
          <GlassCard className="p-6">
            <h3 className="mb-4 text-lg font-semibold text-white">
              Bank Details
            </h3>

            <div className="space-y-3 text-white/80">
              {client.bank_name && (
                <div>
                  <span className="font-medium text-white">Bank Name:</span>{' '}
                  {client.bank_name}
                </div>
              )}
              {client.account_number && (
                <div>
                  <span className="font-medium text-white">
                    Account Number:
                  </span>{' '}
                  {client.account_number}
                </div>
              )}
              {client.iban && (
                <div>
                  <span className="font-medium text-white">IBAN:</span>{' '}
                  {client.iban}
                </div>
              )}
              {client.swift && (
                <div>
                  <span className="font-medium text-white">SWIFT/BIC:</span>{' '}
                  {client.swift}
                </div>
              )}
            </div>
          </GlassCard>
        )}

        {/* Contacts */}
        {client.contacts && client.contacts.length > 0 && (
          <GlassCard className="p-6 lg:col-span-2">
            <h3 className="mb-4 text-lg font-semibold text-white">Contacts</h3>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              {client.contacts.map((contact, index) => (
                <div key={contact.id} className="rounded-lg bg-white/5 p-4">
                  <div className="mb-2 flex items-center gap-2">
                    <h4 className="font-medium text-white">{contact.name}</h4>
                    {contact.is_default_contact && (
                      <GlassBadge variant="success" size="sm">
                        Default
                      </GlassBadge>
                    )}
                  </div>

                  <div className="space-y-1 text-sm text-white/70">
                    {contact.position && <div>📋 {contact.position}</div>}
                    {contact.email && <div>✉️ {contact.email}</div>}
                    {contact.phone && <div>📞 {contact.phone}</div>}
                  </div>
                </div>
              ))}
            </div>
          </GlassCard>
        )}

        {/* Metadata */}
        <GlassCard className="p-6 lg:col-span-2">
          <h3 className="mb-4 text-lg font-semibold text-white">Metadata</h3>

          <div className="grid grid-cols-1 gap-4 text-white/80 md:grid-cols-2">
            <div>
              <span className="font-medium text-white">Created:</span>{' '}
              {new Date(client.created_at).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
              })}
            </div>
            <div>
              <span className="font-medium text-white">Last Updated:</span>{' '}
              {new Date(client.updated_at).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
              })}
            </div>
          </div>
        </GlassCard>
      </div>
    </div>
  );
}
