'use client';

import React, { useState, useEffect } from 'react';
import {
  ClientWithRelations,
  ClientFilters,
  ClientStats,
  CRMResponse,
} from '@/types/crm';
import {
  GlassCard,
  GlassInput,
  GlassSelect,
  GlassButton,
  GlassBadge,
} from '@/components/ui/glass';
import {
  COUNTRY_OPTIONS,
  VAT_STATUS_OPTIONS,
  CLIENT_TYPE_OPTIONS,
  getCountryName,
} from '@/lib/countries';
import { formatICO, formatDIC } from '@/lib/ares';
import { supabase } from '@/lib/supabase';

interface ClientListProps {
  onClientSelect: (client: ClientWithRelations) => void;
  onClientEdit: (client: ClientWithRelations) => void;
  onClientDelete: (client: ClientWithRelations) => void;
  onCreateNew: () => void;
}

export function ClientList({
  onClientSelect,
  onClientEdit,
  onClientDelete,
  onCreateNew,
}: ClientListProps) {
  const [clients, setClients] = useState<ClientWithRelations[]>([]);
  const [stats, setStats] = useState<ClientStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<ClientFilters>({
    page: 1,
    limit: 20,
    sort_by: 'name',
    sort_order: 'asc',
  });

  // Helper function to get access token for API calls
  const getAccessToken = async (): Promise<string | null> => {
    try {
      const { data: { session: supabaseSession } } = await supabase.auth.getSession();
      return supabaseSession?.access_token || null;
    } catch (error) {
      console.error('Error getting access token:', error);
      return null;
    }
  };

  // Fetch clients
  const fetchClients = async () => {
    setLoading(true);
    setError(null);

    try {
      const accessToken = await getAccessToken();
      if (!accessToken) {
        setError('Authentication required');
        setLoading(false);
        return;
      }

      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });

      const response = await fetch(`/api/clients?${params.toString()}`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      const result: CRMResponse = await response.json();

      if (result.success && result.data) {
        setClients(result.data.clients);
        setStats(result.data.stats);
      } else {
        setError(result.error || 'Failed to fetch clients');
      }
    } catch (err) {
      setError('Failed to connect to server');
    } finally {
      setLoading(false);
    }
  };

  // Effect to fetch clients when filters change
  useEffect(() => {
    fetchClients();
  }, [filters]);

  // Handle filter changes
  const handleFilterChange = (key: keyof ClientFilters, value: any) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
      page: key !== 'page' ? 1 : value, // Reset to page 1 when other filters change
    }));
  };

  // Handle search with debounce
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(
    null,
  );
  const handleSearchChange = (value: string) => {
    if (searchTimeout) clearTimeout(searchTimeout);

    const timeout = setTimeout(() => {
      handleFilterChange('search', value || undefined);
    }, 500);

    setSearchTimeout(timeout);
  };

  // Get VAT status badge variant
  const getVATStatusVariant = (status?: string) => {
    switch (status) {
      case 'vat_payer':
        return 'success';
      case 'non_vat_payer':
        return 'warning';
      default:
        return 'default';
    }
  };

  // Format client address
  const formatAddress = (client: ClientWithRelations) => {
    const parts = [client.street, client.city, client.postal_code].filter(
      Boolean,
    );
    return parts.join(', ');
  };

  if (loading && clients.length === 0) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-gray-600">Loading clients...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with stats */}
      {stats && (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
          <GlassCard className="p-4">
            <div className="text-2xl font-bold text-gray-900">
              {stats.total_clients}
            </div>
            <div className="text-gray-600">Total Clients</div>
          </GlassCard>

          <GlassCard className="p-4">
            <div className="text-2xl font-bold text-gray-900">
              {Object.keys(stats.by_country).length}
            </div>
            <div className="text-gray-600">Countries</div>
          </GlassCard>

          <GlassCard className="p-4">
            <div className="text-2xl font-bold text-gray-900">
              {stats.by_type.company || 0}
            </div>
            <div className="text-gray-600">Companies</div>
          </GlassCard>

          <GlassCard className="p-4">
            <div className="text-2xl font-bold text-gray-900">
              {stats.by_type.freelancer || 0}
            </div>
            <div className="text-gray-600">Freelancers</div>
          </GlassCard>
        </div>
      )}

      {/* Filters and Actions */}
      <GlassCard className="p-6">
        <div className="flex flex-col items-start justify-between gap-4 lg:flex-row lg:items-end">
          <div className="grid flex-1 grid-cols-1 gap-4 md:grid-cols-4">
            <GlassInput
              placeholder="Search clients..."
              onChange={(e) => handleSearchChange(e.target.value)}
            />

            <GlassSelect
              options={[
                { value: '', label: 'All Countries' },
                ...COUNTRY_OPTIONS,
              ]}
              value={filters.country || ''}
              onChange={(e) =>
                handleFilterChange('country', e.target.value || undefined)
              }
            />

            <GlassSelect
              options={[
                { value: '', label: 'All Types' },
                ...CLIENT_TYPE_OPTIONS,
              ]}
              value={filters.type || ''}
              onChange={(e) =>
                handleFilterChange('type', e.target.value || undefined)
              }
            />

            <GlassSelect
              options={[
                { value: '', label: 'All VAT Status' },
                ...VAT_STATUS_OPTIONS,
              ]}
              value={filters.vat_status || ''}
              onChange={(e) =>
                handleFilterChange('vat_status', e.target.value || undefined)
              }
            />
          </div>

          <GlassButton variant="primary" onClick={onCreateNew}>
            Add Client
          </GlassButton>
        </div>
      </GlassCard>

      {/* Error State */}
      {error && (
        <GlassCard className="p-6">
          <div className="text-center text-red-600">
            {error}
            <div className="mt-2">
              <GlassButton variant="secondary" size="sm" onClick={fetchClients}>
                Retry
              </GlassButton>
            </div>
          </div>
        </GlassCard>
      )}

      {/* Client List */}
      {clients.length === 0 && !loading && !error ? (
        <GlassCard className="p-12">
          <div className="text-center text-gray-600">
            <div className="mb-2 text-xl">No clients found</div>
            <div className="mb-4">Get started by adding your first client</div>
            <GlassButton variant="primary" onClick={onCreateNew}>
              Add Your First Client
            </GlassButton>
          </div>
        </GlassCard>
      ) : (
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2 xl:grid-cols-3">
          {clients.map((client) => (
            <GlassCard
              key={client.id}
              className="cursor-pointer p-6 transition-transform duration-300 hover:scale-105"
              onClick={() => onClientSelect(client)}
            >
              <div className="mb-4 flex items-start justify-between">
                <div>
                  <h3 className="mb-1 text-lg font-semibold text-gray-900">
                    {client.name}
                  </h3>
                  <div className="flex items-center gap-2">
                    <GlassBadge variant="info" size="sm">
                      {getCountryName(client.country)}
                    </GlassBadge>
                    <GlassBadge variant="default" size="sm">
                      {client.type}
                    </GlassBadge>
                    {client.vat_status && (
                      <GlassBadge
                        variant={getVATStatusVariant(client.vat_status)}
                        size="sm"
                      >
                        {client.vat_status.replace('_', ' ')}
                      </GlassBadge>
                    )}
                  </div>
                </div>

                <div className="flex gap-2">
                  <GlassButton
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      onClientEdit(client);
                    }}
                  >
                    Edit
                  </GlassButton>
                  <GlassButton
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      onClientDelete(client);
                    }}
                  >
                    Delete
                  </GlassButton>
                </div>
              </div>

              <div className="space-y-2 text-sm text-gray-600">
                {formatAddress(client) && <div>📍 {formatAddress(client)}</div>}

                {client.invoice_email && <div>✉️ {client.invoice_email}</div>}

                {client.ico && <div>🏢 IČO: {formatICO(client.ico)}</div>}

                {client.dic && <div>💼 DIČ: {formatDIC(client.dic)}</div>}

                {client.vat_number && <div>🔢 VAT: {client.vat_number}</div>}

                {client.contacts && client.contacts.length > 0 && (
                  <div>
                    👥 {client.contacts.length} contact
                    {client.contacts.length !== 1 ? 's' : ''}
                  </div>
                )}

                {client.tags && client.tags.length > 0 && (
                  <div className="mt-2 flex flex-wrap gap-1">
                    {client.tags.map((tag, index) => (
                      <GlassBadge key={index} variant="default" size="sm">
                        {tag}
                      </GlassBadge>
                    ))}
                  </div>
                )}
              </div>
            </GlassCard>
          ))}
        </div>
      )}

      {/* Pagination */}
      {clients.length > 0 && (
        <div className="flex justify-center">
          <div className="flex gap-2">
            <GlassButton
              variant="ghost"
              size="sm"
              disabled={filters.page === 1}
              onClick={() =>
                handleFilterChange('page', (filters.page || 1) - 1)
              }
            >
              Previous
            </GlassButton>

            <div className="flex items-center px-4 py-2 text-gray-700">
              Page {filters.page || 1}
            </div>

            <GlassButton
              variant="ghost"
              size="sm"
              onClick={() =>
                handleFilterChange('page', (filters.page || 1) + 1)
              }
            >
              Next
            </GlassButton>
          </div>
        </div>
      )}
    </div>
  );
}
