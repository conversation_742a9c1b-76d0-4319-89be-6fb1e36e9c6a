'use client';

import React, { useState, useEffect } from 'react';
import {
  ClientFormData,
  ClientType,
  CountryCode,
  VATStatus,
  ARESResponse,
} from '@/types/crm';
import {
  GlassCard,
  GlassInput,
  GlassSelect,
  GlassTextarea,
  GlassButton,
} from '@/components/ui/glass';
import {
  COUNTRY_OPTIONS,
  VAT_STATUS_OPTIONS,
  CLIENT_TYPE_OPTIONS,
  getCountrySpecificFields,
  isEUCountry,
} from '@/lib/countries';
import { validateICOChecksum } from '@/lib/ares';

interface ClientFormProps {
  initialData?: Partial<ClientFormData>;
  onSubmit: (data: ClientFormData) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
  mode: 'create' | 'edit';
}

export function ClientForm({
  initialData,
  onSubmit,
  onCancel,
  loading = false,
  mode,
}: ClientFormProps) {
  const [formData, setFormData] = useState<ClientFormData>({
    type: 'company',
    name: '',
    country: 'CZ',
    city: '',
    use_same_address: true,
    contacts: [
      {
        name: '',
        email: '',
        phone: '',
        position: '',
        is_default_contact: true,
      },
    ],
    ...initialData,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [aresLoading, setAresLoading] = useState(false);

  // Get country-specific fields
  const countryFields = getCountrySpecificFields(formData.country);
  const isEU = isEUCountry(formData.country);
  const isCzech = formData.country === 'CZ';

  // Handle form field changes
  const handleChange = (field: keyof ClientFormData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));

    // Clear error when field is changed
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: '' }));
    }
  };

  // Handle contact changes
  const handleContactChange = (index: number, field: string, value: any) => {
    const newContacts = [...formData.contacts];
    newContacts[index] = { ...newContacts[index], [field]: value };

    // If setting as default, unset others
    if (field === 'is_default_contact' && value) {
      newContacts.forEach((contact, i) => {
        if (i !== index) contact.is_default_contact = false;
      });
    }

    setFormData((prev) => ({ ...prev, contacts: newContacts }));
  };

  // Add new contact
  const addContact = () => {
    setFormData((prev) => ({
      ...prev,
      contacts: [
        ...prev.contacts,
        {
          name: '',
          email: '',
          phone: '',
          position: '',
          is_default_contact: false,
        },
      ],
    }));
  };

  // Remove contact
  const removeContact = (index: number) => {
    if (formData.contacts.length > 1) {
      const newContacts = formData.contacts.filter((_, i) => i !== index);
      // If we removed the default contact, make the first one default
      if (
        formData.contacts[index].is_default_contact &&
        newContacts.length > 0
      ) {
        newContacts[0].is_default_contact = true;
      }
      setFormData((prev) => ({ ...prev, contacts: newContacts }));
    }
  };

  // Fetch data from ARES
  const fetchFromARES = async () => {
    if (!formData.ico || !isCzech) return;

    const cleanICO = formData.ico.replace(/\D/g, '');
    if (cleanICO.length !== 8 || !validateICOChecksum(cleanICO)) {
      setErrors((prev) => ({ ...prev, ico: 'Invalid ICO format or checksum' }));
      return;
    }

    setAresLoading(true);
    setErrors((prev) => ({ ...prev, ico: '' }));

    try {
      const response = await fetch('/api/ares', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${localStorage.getItem('access_token')}`,
        },
        body: JSON.stringify({ ico: cleanICO }),
      });

      const result: ARESResponse = await response.json();

      if (result.success && result.data) {
        // Pre-fill form with ARES data
        setFormData((prev) => ({
          ...prev,
          name: result.data!.name,
          street: result.data!.address.street,
          city: result.data!.address.city,
          postal_code: result.data!.address.postal_code,
          dic: result.data!.dic || prev.dic,
          vat_status: result.data!.vat_status,
          registration_number:
            result.data!.registration_number || prev.registration_number,
        }));
      } else {
        setErrors((prev) => ({
          ...prev,
          ico: result.error || 'Failed to fetch data from ARES',
        }));
      }
    } catch (error) {
      setErrors((prev) => ({
        ...prev,
        ico: 'Failed to connect to ARES service',
      }));
    } finally {
      setAresLoading(false);
    }
  };

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) newErrors.name = 'Name is required';
    if (!formData.city.trim()) newErrors.city = 'City is required';
    if (!formData.country) newErrors.country = 'Country is required';

    // Country-specific validation
    if (isCzech) {
      if (formData.ico && !/^[0-9]{8}$/.test(formData.ico.replace(/\D/g, ''))) {
        newErrors.ico = 'ICO must be 8 digits';
      }
      if (formData.dic && !/^CZ[0-9]{8,10}$/.test(formData.dic)) {
        newErrors.dic = 'DIC must be in format CZ followed by 8-10 digits';
      }
    }

    // Contact validation
    formData.contacts.forEach((contact, index) => {
      if (!contact.name.trim()) {
        newErrors[`contact_${index}_name`] = 'Contact name is required';
      }
      if (contact.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(contact.email)) {
        newErrors[`contact_${index}_email`] = 'Invalid email format';
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <GlassCard className="p-6">
        <h3 className="mb-4 text-lg font-semibold text-white">
          Basic Information
        </h3>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <GlassSelect
            label="Client Type"
            value={formData.type}
            onChange={(e) => handleChange('type', e.target.value as ClientType)}
            options={CLIENT_TYPE_OPTIONS}
            error={errors.type}
            required
          />

          <GlassSelect
            label="Country"
            value={formData.country}
            onChange={(e) =>
              handleChange('country', e.target.value as CountryCode)
            }
            options={COUNTRY_OPTIONS}
            error={errors.country}
            required
          />
        </div>

        <div className="mt-4">
          <GlassInput
            label="Name"
            value={formData.name}
            onChange={(e) => handleChange('name', e.target.value)}
            error={errors.name}
            placeholder="Enter client name"
            required
          />
        </div>
      </GlassCard>

      {/* Country-specific fields */}
      {countryFields.length > 0 && (
        <GlassCard className="p-6">
          <h3 className="mb-4 text-lg font-semibold text-white">
            {isCzech
              ? 'Czech Business Information'
              : isEU
                ? 'EU Tax Information'
                : 'Tax Information'}
          </h3>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            {isCzech && (
              <>
                <div className="space-y-2">
                  <GlassInput
                    label="IČO"
                    value={formData.ico || ''}
                    onChange={(e) => handleChange('ico', e.target.value)}
                    error={errors.ico}
                    placeholder="12345678"
                  />
                  {formData.ico && formData.ico.length >= 8 && (
                    <GlassButton
                      type="button"
                      variant="secondary"
                      size="sm"
                      onClick={fetchFromARES}
                      loading={aresLoading}
                    >
                      Fetch from ARES
                    </GlassButton>
                  )}
                </div>

                <GlassInput
                  label="DIČ"
                  value={formData.dic || ''}
                  onChange={(e) => handleChange('dic', e.target.value)}
                  error={errors.dic}
                  placeholder="**********"
                />

                <GlassInput
                  label="Registration Number"
                  value={formData.registration_number || ''}
                  onChange={(e) =>
                    handleChange('registration_number', e.target.value)
                  }
                  placeholder="Optional registration number"
                />

                <GlassSelect
                  label="VAT Status"
                  value={formData.vat_status || ''}
                  onChange={(e) =>
                    handleChange('vat_status', e.target.value as VATStatus)
                  }
                  options={[
                    { value: '', label: 'Select VAT status' },
                    ...VAT_STATUS_OPTIONS,
                  ]}
                />
              </>
            )}

            {isEU && !isCzech && (
              <>
                <GlassInput
                  label="VAT Number"
                  value={formData.vat_number || ''}
                  onChange={(e) => handleChange('vat_number', e.target.value)}
                  error={errors.vat_number}
                  placeholder={`${formData.country}123456789`}
                />

                <GlassSelect
                  label="VAT Status"
                  value={formData.vat_status || ''}
                  onChange={(e) =>
                    handleChange('vat_status', e.target.value as VATStatus)
                  }
                  options={[
                    { value: '', label: 'Select VAT status' },
                    ...VAT_STATUS_OPTIONS,
                  ]}
                />
              </>
            )}

            {!isEU && (
              <GlassInput
                label="Tax ID"
                value={formData.tax_id || ''}
                onChange={(e) => handleChange('tax_id', e.target.value)}
                placeholder="Tax identification number"
              />
            )}
          </div>
        </GlassCard>
      )}

      {/* Address Information */}
      <GlassCard className="p-6">
        <h3 className="mb-4 text-lg font-semibold text-white">
          Address Information
        </h3>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <GlassInput
            label="Street"
            value={formData.street || ''}
            onChange={(e) => handleChange('street', e.target.value)}
            placeholder="Street address"
          />

          <GlassInput
            label="City"
            value={formData.city}
            onChange={(e) => handleChange('city', e.target.value)}
            error={errors.city}
            placeholder="City"
            required
          />

          <GlassInput
            label="Postal Code"
            value={formData.postal_code || ''}
            onChange={(e) => handleChange('postal_code', e.target.value)}
            placeholder="Postal code"
          />
        </div>

        <div className="mt-4">
          <label className="flex items-center space-x-2 text-white/80">
            <input
              type="checkbox"
              checked={formData.use_same_address}
              onChange={(e) =>
                handleChange('use_same_address', e.target.checked)
              }
              className="rounded border-white/20 bg-white/10 text-blue-500 focus:ring-blue-400/50"
            />
            <span>Use same address for invoicing</span>
          </label>
        </div>

        {!formData.use_same_address && (
          <div className="mt-4 space-y-4">
            <h4 className="text-md font-medium text-white/90">
              Invoice Address
            </h4>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <GlassInput
                label="Invoice Street"
                value={formData.invoice_street || ''}
                onChange={(e) => handleChange('invoice_street', e.target.value)}
                placeholder="Invoice street address"
              />

              <GlassInput
                label="Invoice City"
                value={formData.invoice_city || ''}
                onChange={(e) => handleChange('invoice_city', e.target.value)}
                placeholder="Invoice city"
              />

              <GlassInput
                label="Invoice Postal Code"
                value={formData.invoice_postal_code || ''}
                onChange={(e) =>
                  handleChange('invoice_postal_code', e.target.value)
                }
                placeholder="Invoice postal code"
              />

              <GlassSelect
                label="Invoice Country"
                value={formData.invoice_country || formData.country}
                onChange={(e) =>
                  handleChange('invoice_country', e.target.value as CountryCode)
                }
                options={COUNTRY_OPTIONS}
              />
            </div>
          </div>
        )}
      </GlassCard>

      {/* Bank Details */}
      <GlassCard className="p-6">
        <h3 className="mb-4 text-lg font-semibold text-white">Bank Details</h3>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <GlassInput
            label="Bank Name"
            value={formData.bank_name || ''}
            onChange={(e) => handleChange('bank_name', e.target.value)}
            placeholder="Bank name"
          />

          <GlassInput
            label="Account Number"
            value={formData.account_number || ''}
            onChange={(e) => handleChange('account_number', e.target.value)}
            placeholder="Account number"
          />

          <GlassInput
            label="IBAN"
            value={formData.iban || ''}
            onChange={(e) => handleChange('iban', e.target.value)}
            error={errors.iban}
            placeholder="GB29 NWBK 6016 1331 9268 19"
          />

          <GlassInput
            label="SWIFT/BIC"
            value={formData.swift || ''}
            onChange={(e) => handleChange('swift', e.target.value)}
            placeholder="SWIFT/BIC code"
          />
        </div>
      </GlassCard>

      {/* Contact Information */}
      <GlassCard className="p-6">
        <div className="mb-4 flex items-center justify-between">
          <h3 className="text-lg font-semibold text-white">Contacts</h3>
          <GlassButton
            type="button"
            variant="secondary"
            size="sm"
            onClick={addContact}
          >
            Add Contact
          </GlassButton>
        </div>

        <div className="space-y-4">
          {formData.contacts.map((contact, index) => (
            <div
              key={index}
              className="rounded-lg border border-white/20 bg-white/5 p-4"
            >
              <div className="mb-3 flex items-center justify-between">
                <h4 className="text-md font-medium text-white/90">
                  Contact {index + 1}
                </h4>
                {formData.contacts.length > 1 && (
                  <GlassButton
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeContact(index)}
                  >
                    Remove
                  </GlassButton>
                )}
              </div>

              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <GlassInput
                  label="Name"
                  value={contact.name}
                  onChange={(e) =>
                    handleContactChange(index, 'name', e.target.value)
                  }
                  error={errors[`contact_${index}_name`]}
                  placeholder="Contact name"
                  required
                />

                <GlassInput
                  label="Email"
                  type="email"
                  value={contact.email || ''}
                  onChange={(e) =>
                    handleContactChange(index, 'email', e.target.value)
                  }
                  error={errors[`contact_${index}_email`]}
                  placeholder="<EMAIL>"
                />

                <GlassInput
                  label="Phone"
                  value={contact.phone || ''}
                  onChange={(e) =>
                    handleContactChange(index, 'phone', e.target.value)
                  }
                  placeholder="+420 123 456 789"
                />

                <GlassInput
                  label="Position"
                  value={contact.position || ''}
                  onChange={(e) =>
                    handleContactChange(index, 'position', e.target.value)
                  }
                  placeholder="Job title"
                />
              </div>

              <div className="mt-3">
                <label className="flex items-center space-x-2 text-white/80">
                  <input
                    type="checkbox"
                    checked={contact.is_default_contact}
                    onChange={(e) =>
                      handleContactChange(
                        index,
                        'is_default_contact',
                        e.target.checked,
                      )
                    }
                    className="rounded border-white/20 bg-white/10 text-blue-500 focus:ring-blue-400/50"
                  />
                  <span>Default contact</span>
                </label>
              </div>
            </div>
          ))}
        </div>
      </GlassCard>

      {/* Additional Information */}
      <GlassCard className="p-6">
        <h3 className="mb-4 text-lg font-semibold text-white">
          Additional Information
        </h3>

        <div className="space-y-4">
          <GlassInput
            label="Invoice Email"
            type="email"
            value={formData.invoice_email || ''}
            onChange={(e) => handleChange('invoice_email', e.target.value)}
            error={errors.invoice_email}
            placeholder="<EMAIL>"
          />

          <GlassTextarea
            label="Notes"
            value={formData.notes || ''}
            onChange={(e) => handleChange('notes', e.target.value)}
            placeholder="Additional notes about this client..."
            rows={3}
          />

          <GlassInput
            label="Tags"
            value={formData.tags?.join(', ') || ''}
            onChange={(e) =>
              handleChange(
                'tags',
                e.target.value
                  .split(',')
                  .map((tag) => tag.trim())
                  .filter(Boolean),
              )
            }
            placeholder="VIP, Regular, New (comma-separated)"
          />
        </div>
      </GlassCard>

      {/* Form Actions */}
      <div className="flex justify-end space-x-4">
        <GlassButton
          type="button"
          variant="ghost"
          onClick={onCancel}
          disabled={loading}
        >
          Cancel
        </GlassButton>

        <GlassButton type="submit" variant="primary" loading={loading}>
          {mode === 'create' ? 'Create Client' : 'Update Client'}
        </GlassButton>
      </div>
    </form>
  );
}
