'use client';

import React, { useState } from 'react';
import { ClientWithRelations, ClientFormData, CRMResponse } from '@/types/crm';
import { ClientList } from './ClientList';
import { ClientForm } from './ClientForm';
import { ClientDetails } from './ClientDetails';
import { GlassModal } from '@/components/ui/glass';

type ViewMode = 'list' | 'create' | 'edit' | 'details';

export function CRMPage() {
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [selectedClient, setSelectedClient] =
    useState<ClientWithRelations | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Handle client selection
  const handleClientSelect = (client: ClientWithRelations) => {
    setSelectedClient(client);
    setViewMode('details');
  };

  // Handle client edit
  const handleClientEdit = (client: ClientWithRelations) => {
    setSelectedClient(client);
    setViewMode('edit');
  };

  // Handle client delete
  const handleClientDelete = async (client: ClientWithRelations) => {
    if (!confirm(`Are you sure you want to delete ${client.name}?`)) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/clients/${client.id}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${localStorage.getItem('access_token')}`,
        },
      });

      const result: CRMResponse = await response.json();

      if (result.success) {
        // Refresh the list by going back to list view
        setViewMode('list');
        setSelectedClient(null);
      } else {
        setError(result.error || 'Failed to delete client');
      }
    } catch (err) {
      setError('Failed to connect to server');
    } finally {
      setLoading(false);
    }
  };

  // Handle create new client
  const handleCreateNew = () => {
    setSelectedClient(null);
    setViewMode('create');
  };

  // Handle form submission (create or update)
  const handleFormSubmit = async (data: ClientFormData) => {
    setLoading(true);
    setError(null);

    try {
      const url =
        viewMode === 'edit' && selectedClient
          ? `/api/clients/${selectedClient.id}`
          : '/api/clients';

      const method = viewMode === 'edit' ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${localStorage.getItem('access_token')}`,
        },
        body: JSON.stringify(data),
      });

      const result: CRMResponse = await response.json();

      if (result.success) {
        // Go back to list view and refresh
        setViewMode('list');
        setSelectedClient(null);
      } else {
        setError(result.error || `Failed to ${viewMode} client`);
      }
    } catch (err) {
      setError('Failed to connect to server');
    } finally {
      setLoading(false);
    }
  };

  // Handle form cancel
  const handleFormCancel = () => {
    setViewMode('list');
    setSelectedClient(null);
    setError(null);
  };

  // Handle back to list from details
  const handleBackToList = () => {
    setViewMode('list');
    setSelectedClient(null);
  };

  // Convert client to form data for editing
  const clientToFormData = (client: ClientWithRelations): ClientFormData => {
    return {
      type: client.type,
      name: client.name,
      country: client.country,
      street: client.street || '',
      city: client.city,
      postal_code: client.postal_code || '',
      use_same_address: !client.invoice_street && !client.invoice_city,
      invoice_street: client.invoice_street || '',
      invoice_city: client.invoice_city || '',
      invoice_postal_code: client.invoice_postal_code || '',
      invoice_country: client.invoice_country || client.country,
      bank_name: client.bank_name || '',
      account_number: client.account_number || '',
      iban: client.iban || '',
      swift: client.swift || '',
      invoice_email: client.invoice_email || '',
      notes: client.notes || '',
      tags: client.tags || [],
      ico: client.ico || '',
      dic: client.dic || '',
      registration_number: client.registration_number || '',
      vat_status: client.vat_status,
      vat_number: client.vat_number || '',
      tax_id: client.tax_id || '',
      contacts: client.contacts?.map((contact) => ({
        name: contact.name,
        email: contact.email || '',
        phone: contact.phone || '',
        position: contact.position || '',
        is_default_contact: contact.is_default_contact,
      })) || [
        {
          name: '',
          email: '',
          phone: '',
          position: '',
          is_default_contact: true,
        },
      ],
    };
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-6">
      <div className="mx-auto max-w-7xl">
        {/* Header */}
        <div className="mb-8">
          <h1 className="mb-2 text-3xl font-bold text-white">
            Client Management
          </h1>
          <p className="text-white/70">
            Manage your international clients with Czech and EU compliance
          </p>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-6 rounded-lg border border-red-400/30 bg-red-500/20 p-4 text-red-100">
            {error}
            <button
              onClick={() => setError(null)}
              className="ml-4 text-red-200 hover:text-white"
            >
              ✕
            </button>
          </div>
        )}

        {/* Main Content */}
        {viewMode === 'list' && (
          <ClientList
            onClientSelect={handleClientSelect}
            onClientEdit={handleClientEdit}
            onClientDelete={handleClientDelete}
            onCreateNew={handleCreateNew}
          />
        )}

        {viewMode === 'details' && selectedClient && (
          <ClientDetails
            client={selectedClient}
            onEdit={() => handleClientEdit(selectedClient)}
            onDelete={() => handleClientDelete(selectedClient)}
            onBack={handleBackToList}
          />
        )}

        {/* Form Modal */}
        <GlassModal
          isOpen={viewMode === 'create' || viewMode === 'edit'}
          onClose={handleFormCancel}
          title={viewMode === 'create' ? 'Create New Client' : 'Edit Client'}
          size="xl"
        >
          <ClientForm
            initialData={
              viewMode === 'edit' && selectedClient
                ? clientToFormData(selectedClient)
                : undefined
            }
            onSubmit={handleFormSubmit}
            onCancel={handleFormCancel}
            loading={loading}
            mode={viewMode === 'create' ? 'create' : 'edit'}
          />
        </GlassModal>
      </div>
    </div>
  );
}
