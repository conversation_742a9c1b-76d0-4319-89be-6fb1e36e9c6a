'use client';

import React from 'react';
import Link from 'next/link';
import { useTranslations } from 'next-intl';

interface CRMLayoutProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  breadcrumbs?: Array<{
    label: string;
    href?: string;
  }>;
}

export function CRMLayout({ 
  children, 
  title = 'Client Management', 
  subtitle = 'Manage your clients and business relationships',
  breadcrumbs = []
}: CRMLayoutProps) {
  const t = useTranslations('CRM');

  const defaultBreadcrumbs = [
    { label: 'Dashboard', href: '/en/dashboard' },
    { label: 'CRM', href: '/en/crm' },
    ...breadcrumbs
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumbs */}
      <div className="border-b border-gray-200 bg-white">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <nav className="flex py-4" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-4">
              {defaultBreadcrumbs.map((crumb, index) => (
                <li key={index}>
                  <div className="flex items-center">
                    {index > 0 && (
                      <svg
                        className="h-5 w-5 flex-shrink-0 text-gray-400"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        aria-hidden="true"
                      >
                        <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                      </svg>
                    )}
                    {crumb.href ? (
                      <Link
                        href={crumb.href}
                        className="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700"
                      >
                        {crumb.label}
                      </Link>
                    ) : (
                      <span className="ml-4 text-sm font-medium text-gray-900">
                        {crumb.label}
                      </span>
                    )}
                  </div>
                </li>
              ))}
            </ol>
          </nav>
        </div>
      </div>

      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
              <p className="mt-1 text-sm text-gray-600">{subtitle}</p>
            </div>
            <div className="flex items-center space-x-2">
              <span className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
                Invoice Hub
              </span>
              <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                CRM Module
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <main className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        {children}
      </main>
    </div>
  );
}
