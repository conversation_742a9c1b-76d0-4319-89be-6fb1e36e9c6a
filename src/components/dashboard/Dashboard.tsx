'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { AuthSession, UserProfile } from '@/types/auth';
import { authStorage } from '@/lib/auth-storage';
import { supabase } from '@/lib/supabase';
import { DashboardHeader } from './DashboardHeader';
import { DashboardContent } from './DashboardContent';

export function Dashboard() {
  const router = useRouter();
  const t = useTranslations('Dashboard');
  const [session, setSession] = useState<AuthSession | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadSession = async () => {
      try {
        // First try to get session from secure storage
        const storedSession = await authStorage.get();
        if (storedSession) {
          setSession(storedSession);
          setLoading(false);
          return;
        }

        // If no stored session, check Supabase auth
        const {
          data: { session: supabaseSession },
          error: sessionError,
        } = await supabase.auth.getSession();

        // Handle invalid refresh token error
        if (sessionError && sessionError.message.includes('Invalid Refresh Token')) {
          console.log('Invalid refresh token detected, clearing session and redirecting to login');
          await authStorage.clear();
          await supabase.auth.signOut();
          router.push('/auth/login');
          return;
        }

        if (!supabaseSession) {
          // No session found, redirect to login
          router.push('/auth/login');
          return;
        }

        // Debug: Log user metadata to see what's available
        console.log('Supabase user:', supabaseSession.user);
        console.log('User metadata:', supabaseSession.user.user_metadata);

        // Fetch user profile from database
        const { data: userProfileData, error } = await supabase
          .from('user_profiles')
          .select('*')
          .eq('id', supabaseSession.user.id)
          .single();

        if (error) {
          console.error('Error fetching user profile:', error);
        }

        console.log('User profile from database:', userProfileData);

        // Create a session with user data from database or fallback to Supabase metadata
        const userProfile: UserProfile = {
          id: supabaseSession.user.id,
          email: supabaseSession.user.email || '',
          full_name: userProfileData?.full_name ||
                    supabaseSession.user.user_metadata?.full_name ||
                    supabaseSession.user.user_metadata?.name ||
                    supabaseSession.user.email?.split('@')[0] || 'User',
          account_type: userProfileData?.account_type || 'freelancer',
          phone: userProfileData?.phone || null,
          avatar_url: userProfileData?.avatar_url ||
                     supabaseSession.user.user_metadata?.avatar_url || null,
          locale: userProfileData?.locale || 'en',
          timezone: userProfileData?.timezone || 'UTC',
          onboarding_completed: userProfileData?.onboarding_completed || false,
          created_at: supabaseSession.user.created_at,
          updated_at: supabaseSession.user.updated_at || supabaseSession.user.created_at,
        };

        const sessionData: AuthSession = {
          user: supabaseSession.user,
          profile: userProfile,
          organizations: [], // Will be loaded separately if needed
          current_organization: null,
          current_role: null,
        };

        setSession(sessionData);
        authStorage.store(sessionData);
      } catch (error) {
        console.error('Error loading session:', error);
        router.push('/auth/login');
        return;
      } finally {
        setLoading(false);
      }
    };

    loadSession();
  }, [router]);

  const handleLogout = async () => {
    try {
      // Sign out from Supabase
      await supabase.auth.signOut();

      // Clear local session storage
      authStorage.clear();

      // Redirect to login page
      router.push('/auth/login');
    } catch (error) {
      console.error('Logout error:', error);
      // Even if there's an error, clear local storage and redirect
      authStorage.clear();
      router.push('/auth/login');
    }
  };

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="mx-auto h-12 w-12 animate-spin rounded-full border-4 border-indigo-600 border-t-transparent"></div>
          <p className="mt-4 text-sm text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (!session) {
    return null; // Will redirect to login
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <DashboardHeader session={session} onLogout={handleLogout} />
      <DashboardContent />
    </div>
  );
}
