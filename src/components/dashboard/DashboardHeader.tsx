'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { AuthSession } from '@/types/auth';

interface DashboardHeaderProps {
  session: AuthSession;
  onLogout: () => void;
}

export function DashboardHeader({ session, onLogout }: DashboardHeaderProps) {
  const t = useTranslations('Dashboard.Header');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const { profile, current_organization, current_role } = session;

  return (
    <header className="border-b border-gray-200 bg-white shadow-sm">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          {/* Logo and App Name */}
          <div className="flex items-center space-x-4">
            <div className="h-8 w-8 rounded-lg bg-gradient-to-br from-blue-600 to-purple-600"></div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">Invoice Hub</h1>
              <p className="text-xs text-gray-500">
                {current_organization?.name}
              </p>
            </div>
          </div>

          {/* Navigation */}
          <nav className="hidden items-center space-x-8 md:flex">
            <a
              href="#"
              className="text-sm font-medium text-gray-700 transition-colors hover:text-gray-900"
            >
              {t('nav.dashboard')}
            </a>
            <a
              href="#"
              className="text-sm font-medium text-gray-700 transition-colors hover:text-gray-900"
            >
              {t('nav.invoices')}
            </a>
            <a
              href="#"
              className="text-sm font-medium text-gray-700 transition-colors hover:text-gray-900"
            >
              {t('nav.clients')}
            </a>
            <a
              href="#"
              className="text-sm font-medium text-gray-700 transition-colors hover:text-gray-900"
            >
              {t('nav.settings')}
            </a>
          </nav>

          {/* User Menu */}
          <div className="relative">
            <button
              onClick={() => setIsDropdownOpen(!isDropdownOpen)}
              className="flex items-center space-x-3 rounded-lg px-3 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-100"
            >
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-indigo-600">
                <span className="text-sm font-medium text-white">
                  {profile?.full_name?.charAt(0)?.toUpperCase() ||
                    profile?.email?.charAt(0)?.toUpperCase() ||
                    'U'}
                </span>
              </div>
              <div className="hidden text-left sm:block">
                <p className="text-sm font-medium text-gray-900">
                  {profile?.full_name || profile?.email || 'User'}
                </p>
                <p className="text-xs text-gray-500 capitalize">
                  {current_role || ''}
                </p>
              </div>
              <svg
                className={`h-4 w-4 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </button>

            {/* Dropdown Menu */}
            {isDropdownOpen && (
              <div className="ring-opacity-5 absolute right-0 z-50 mt-2 w-56 rounded-lg bg-white shadow-lg ring-1 ring-black">
                <div className="py-1">
                  <div className="border-b border-gray-100 px-4 py-3">
                    <p className="text-sm font-medium text-gray-900">
                      {profile?.full_name || profile?.email || 'User'}
                    </p>
                    <p className="text-sm text-gray-500">
                      {profile?.email || ''}
                    </p>
                    <p className="mt-1 text-xs text-gray-400">
                      {current_organization?.name || ''} • {current_role || ''}
                    </p>
                  </div>

                  <a
                    href="#"
                    className="block px-4 py-2 text-sm text-gray-700 transition-colors hover:bg-gray-100"
                  >
                    {t('userMenu.profile')}
                  </a>
                  <a
                    href="#"
                    className="block px-4 py-2 text-sm text-gray-700 transition-colors hover:bg-gray-100"
                  >
                    {t('userMenu.settings')}
                  </a>
                  <a
                    href="#"
                    className="block px-4 py-2 text-sm text-gray-700 transition-colors hover:bg-gray-100"
                  >
                    {t('userMenu.billing')}
                  </a>

                  <div className="border-t border-gray-100">
                    <button
                      onClick={onLogout}
                      className="block w-full px-4 py-2 text-left text-sm text-red-700 transition-colors hover:bg-red-50"
                    >
                      {t('userMenu.logout')}
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}
