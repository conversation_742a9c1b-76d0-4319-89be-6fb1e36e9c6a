'use client';

import Link from 'next/link';
import { useState } from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { LanguageSwitcher } from './LanguageSwitcher';

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const t = useTranslations('Landing.Header');
  const locale = useLocale();

  return (
    <header className="sticky top-0 z-50 border-b border-gray-200 bg-white/95 backdrop-blur-md">
      <div className="container mx-auto flex h-16 items-center justify-between px-4 md:px-6">
        <Link href="/" className="flex items-center space-x-2">
          <div className="h-8 w-8 rounded-lg bg-gradient-to-br from-blue-600 to-purple-600"></div>
          <span className="text-xl font-bold text-gray-900">Invoice Hub</span>
        </Link>

        <nav className="hidden items-center space-x-8 text-sm font-medium text-gray-600 md:flex">
          <Link
            href="#features"
            className="transition-colors hover:text-gray-900"
          >
            {t('features')}
          </Link>
          <Link
            href={`/${locale}/pricing`}
            className="transition-colors hover:text-gray-900"
          >
            {t('pricing')}
          </Link>
          <Link
            href={`/${locale}/help`}
            className="transition-colors hover:text-gray-900"
          >
            {t('help')}
          </Link>
        </nav>

        <div className="flex items-center space-x-4">
          <LanguageSwitcher />
          <Link
            href={`/${locale}/auth/login`}
            className="hidden text-sm font-medium text-gray-600 transition-colors hover:text-gray-900 sm:block"
          >
            {t('signIn')}
          </Link>
          <Link
            href={`/${locale}/auth/signup`}
            className="inline-flex h-10 items-center justify-center rounded-lg bg-gray-900 px-6 text-sm font-medium text-white shadow-sm transition-all hover:bg-gray-800 hover:shadow-md focus-visible:ring-2 focus-visible:ring-gray-950 focus-visible:ring-offset-2 focus-visible:outline-none"
          >
            {t('getStarted')}
          </Link>

          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="inline-flex h-10 w-10 items-center justify-center rounded-lg text-gray-600 hover:bg-gray-100 hover:text-gray-900 md:hidden"
            aria-label={t('toggleMenu')}
            aria-expanded={isMenuOpen}
            aria-controls="mobile-menu"
          >
            <svg
              className="h-5 w-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              {isMenuOpen ? (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              ) : (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              )}
            </svg>
          </button>
        </div>
      </div>

      {isMenuOpen && (
        <div
          id="mobile-menu"
          className="border-t border-gray-200 bg-white md:hidden"
        >
          <div className="container mx-auto px-4 py-4">
            <nav className="flex flex-col space-y-4">
              <div className="flex justify-center border-b border-gray-200 pb-2">
                <LanguageSwitcher />
              </div>
              <Link
                href="#features"
                className="text-sm font-medium text-gray-600 hover:text-gray-900"
                onClick={() => setIsMenuOpen(false)}
              >
                {t('features')}
              </Link>
              <Link
                href={`/${locale}/pricing`}
                className="text-sm font-medium text-gray-600 hover:text-gray-900"
                onClick={() => setIsMenuOpen(false)}
              >
                {t('pricing')}
              </Link>
              <Link
                href={`/${locale}/help`}
                className="text-sm font-medium text-gray-600 hover:text-gray-900"
                onClick={() => setIsMenuOpen(false)}
              >
                {t('help')}
              </Link>
              <Link
                href={`/${locale}/auth/login`}
                className="text-sm font-medium text-gray-600 hover:text-gray-900"
                onClick={() => setIsMenuOpen(false)}
              >
                {t('signIn')}
              </Link>
            </nav>
          </div>
        </div>
      )}
    </header>
  );
}
