'use client';

import React from 'react';
import { cn } from '@/lib/utils';

// Glass Card Component with morphism effect
interface GlassCardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'subtle' | 'strong';
  blur?: 'sm' | 'md' | 'lg' | 'xl';
  children: React.ReactNode;
}

export function GlassCard({
  className,
  variant = 'default',
  blur = 'md',
  children,
  ...props
}: GlassCardProps) {
  const variants = {
    default: 'bg-white/10 border-white/20',
    subtle: 'bg-white/5 border-white/10',
    strong: 'bg-white/20 border-white/30',
  };

  const blurLevels = {
    sm: 'backdrop-blur-sm',
    md: 'backdrop-blur-md',
    lg: 'backdrop-blur-lg',
    xl: 'backdrop-blur-xl',
  };

  return (
    <div
      className={cn(
        'rounded-xl border shadow-lg transition-all duration-300',
        'hover:bg-white/15 hover:shadow-xl',
        variants[variant],
        blurLevels[blur],
        className,
      )}
      {...props}
    >
      {children}
    </div>
  );
}

// Glass Button Component
interface GlassButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  children: React.ReactNode;
}

export function GlassButton({
  className,
  variant = 'primary',
  size = 'md',
  loading = false,
  disabled,
  children,
  ...props
}: GlassButtonProps) {
  const variants = {
    primary:
      'bg-blue-500/20 border-blue-400/30 text-blue-100 hover:bg-blue-500/30 hover:border-blue-400/50',
    secondary:
      'bg-gray-500/20 border-gray-400/30 text-gray-100 hover:bg-gray-500/30 hover:border-gray-400/50',
    ghost:
      'bg-transparent border-white/20 text-white hover:bg-white/10 hover:border-white/30',
  };

  const sizes = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg',
  };

  return (
    <button
      className={cn(
        'rounded-lg border backdrop-blur-md transition-all duration-300',
        'focus:ring-2 focus:ring-blue-400/50 focus:outline-none',
        'disabled:cursor-not-allowed disabled:opacity-50',
        'transform hover:scale-105 active:scale-95',
        variants[variant],
        sizes[size],
        className,
      )}
      disabled={disabled || loading}
      {...props}
    >
      {loading ? (
        <div className="flex items-center justify-center">
          <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
          Loading...
        </div>
      ) : (
        children
      )}
    </button>
  );
}

// Glass Input Component
interface GlassInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  icon?: React.ReactNode;
}

export function GlassInput({
  className,
  label,
  error,
  icon,
  ...props
}: GlassInputProps) {
  return (
    <div className="space-y-2">
      {label && (
        <label className="block text-sm font-medium text-white/80">
          {label}
        </label>
      )}
      <div className="relative">
        {icon && (
          <div className="absolute top-1/2 left-3 -translate-y-1/2 transform text-white/60">
            {icon}
          </div>
        )}
        <input
          className={cn(
            'w-full rounded-lg border px-4 py-3 backdrop-blur-md transition-all duration-300',
            'border-white/20 bg-white/10 text-white placeholder-white/50',
            'focus:border-blue-400/50 focus:ring-2 focus:ring-blue-400/50 focus:outline-none',
            'hover:border-white/30 hover:bg-white/15',
            icon && 'pl-10',
            error && 'border-red-400/50 focus:ring-red-400/50',
            className,
          )}
          {...props}
        />
      </div>
      {error && <p className="text-sm text-red-300">{error}</p>}
    </div>
  );
}

// Glass Select Component
interface GlassSelectProps
  extends React.SelectHTMLAttributes<HTMLSelectElement> {
  label?: string;
  error?: string;
  options: Array<{ value: string; label: string; disabled?: boolean }>;
}

export function GlassSelect({
  className,
  label,
  error,
  options,
  ...props
}: GlassSelectProps) {
  return (
    <div className="space-y-2">
      {label && (
        <label className="block text-sm font-medium text-white/80">
          {label}
        </label>
      )}
      <select
        className={cn(
          'w-full rounded-lg border px-4 py-3 backdrop-blur-md transition-all duration-300',
          'border-white/20 bg-white/10 text-white',
          'focus:border-blue-400/50 focus:ring-2 focus:ring-blue-400/50 focus:outline-none',
          'hover:border-white/30 hover:bg-white/15',
          error && 'border-red-400/50 focus:ring-red-400/50',
          className,
        )}
        {...props}
      >
        {options.map((option) => (
          <option
            key={option.value}
            value={option.value}
            disabled={option.disabled}
            className="bg-gray-800 text-white"
          >
            {option.label}
          </option>
        ))}
      </select>
      {error && <p className="text-sm text-red-300">{error}</p>}
    </div>
  );
}

// Glass Textarea Component
interface GlassTextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  error?: string;
}

export function GlassTextarea({
  className,
  label,
  error,
  ...props
}: GlassTextareaProps) {
  return (
    <div className="space-y-2">
      {label && (
        <label className="block text-sm font-medium text-white/80">
          {label}
        </label>
      )}
      <textarea
        className={cn(
          'w-full rounded-lg border px-4 py-3 backdrop-blur-md transition-all duration-300',
          'border-white/20 bg-white/10 text-white placeholder-white/50',
          'focus:border-blue-400/50 focus:ring-2 focus:ring-blue-400/50 focus:outline-none',
          'hover:border-white/30 hover:bg-white/15',
          'resize-vertical min-h-[100px]',
          error && 'border-red-400/50 focus:ring-red-400/50',
          className,
        )}
        {...props}
      />
      {error && <p className="text-sm text-red-300">{error}</p>}
    </div>
  );
}

// Glass Modal Component
interface GlassModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

export function GlassModal({
  isOpen,
  onClose,
  title,
  children,
  size = 'md',
}: GlassModalProps) {
  const sizes = {
    sm: 'max-w-md',
    md: 'max-w-lg',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl',
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Modal Container */}
      <div
        className="flex min-h-full items-center justify-center p-4"
        onClick={(e) => {
          // Only close if clicking the backdrop, not the modal content
          if (e.target === e.currentTarget) {
            onClose();
          }
        }}
      >
        {/* Backdrop */}
        <div className="absolute inset-0 bg-black/50 backdrop-blur-sm -z-10" />

        {/* Modal */}
        <div
          className={cn(
            'relative w-full rounded-xl border shadow-2xl',
            'border-white/20 bg-white/10 backdrop-blur-md',
            'transform transition-all duration-300',
            'animate-in fade-in-0 zoom-in-95',
            'max-h-[90vh] overflow-hidden flex flex-col',
            'my-8', // Add margin for better spacing
            sizes[size],
          )}
          onClick={(e) => e.stopPropagation()} // Prevent backdrop click when clicking modal
        >
          {title && (
            <div className="border-b border-white/20 px-6 py-4 flex-shrink-0">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-white">{title}</h2>
                <button
                  onClick={onClose}
                  className="text-white/70 hover:text-white transition-colors"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
          )}

          <div className="p-6 overflow-y-auto flex-1">{children}</div>
        </div>
      </div>
    </div>
  );
}

// Glass Badge Component
interface GlassBadgeProps extends React.HTMLAttributes<HTMLSpanElement> {
  variant?: 'default' | 'success' | 'warning' | 'error' | 'info';
  size?: 'sm' | 'md';
  children: React.ReactNode;
}

export function GlassBadge({
  className,
  variant = 'default',
  size = 'sm',
  children,
  ...props
}: GlassBadgeProps) {
  const variants = {
    default: 'bg-gray-500/20 border-gray-400/30 text-gray-100',
    success: 'bg-green-500/20 border-green-400/30 text-green-100',
    warning: 'bg-yellow-500/20 border-yellow-400/30 text-yellow-100',
    error: 'bg-red-500/20 border-red-400/30 text-red-100',
    info: 'bg-blue-500/20 border-blue-400/30 text-blue-100',
  };

  const sizes = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-1.5 text-sm',
  };

  return (
    <span
      className={cn(
        'inline-flex items-center rounded-full border font-medium backdrop-blur-md',
        variants[variant],
        sizes[size],
        className,
      )}
      {...props}
    >
      {children}
    </span>
  );
}
