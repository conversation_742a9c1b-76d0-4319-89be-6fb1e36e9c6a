'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { LoginData, LoginFormState } from '@/types/auth';
import { useTranslations, useLocale } from 'next-intl';
import { supabase } from '@/lib/supabase';

export function LoginForm() {
  const router = useRouter();
  const locale = useLocale();
  const t = useTranslations('Auth.Login');

  const [formState, setFormState] = useState<LoginFormState>({
    loading: false,
    error: null,
  });

  const [formData, setFormData] = useState<LoginData>({
    email: '',
    password: '',
  });

  const [validationErrors, setValidationErrors] = useState<
    Record<string, string>
  >({});

  const handleInputChange = (field: keyof LoginData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));

    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors((prev) => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};

    // Email validation
    if (!formData.email) {
      errors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }

    // Password validation
    if (!formData.password) {
      errors.password = 'Password is required';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Login form submitted', { email: formData.email });

    if (!validateForm()) {
      console.log('Form validation failed');
      return;
    }

    setFormState({ loading: true, error: null });
    console.log('Starting login process...');

    try {
      // Sign in with Supabase Auth directly
      console.log('Attempting Supabase auth...');
      const { data: authData, error: authError } =
        await supabase.auth.signInWithPassword({
          email: formData.email,
          password: formData.password,
        });

      console.log('Supabase auth response:', { authData, authError });

      if (authError) {
        console.error('Auth error:', authError);

        // Handle specific error cases
        if (authError.message.includes('Invalid login credentials')) {
          setFormState({
            loading: false,
            error: 'Invalid email or password',
          });
        } else if (authError.message.includes('Email not confirmed')) {
          setFormState({
            loading: false,
            error: 'Please verify your email address before logging in',
          });
        } else {
          setFormState({
            loading: false,
            error: authError.message,
          });
        }
        return;
      }

      if (!authData.user) {
        console.log('No user data returned');
        setFormState({
          loading: false,
          error: 'Login failed',
        });
        return;
      }

      // Clear loading state before redirect
      console.log('Login successful, redirecting to dashboard...');
      setFormState({ loading: false, error: null });

      // Redirect to dashboard on successful login
      router.push(`/${locale}/dashboard`);
    } catch (error) {
      console.error('Login error:', error);
      setFormState({
        loading: false,
        error: 'Network error. Please try again.',
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900">
          Sign in to your account
        </h2>
        <p className="mt-2 text-sm text-gray-600">
          Or{' '}
          <Link
            href={`/${locale}/auth/signup`}
            className="font-medium text-indigo-600 hover:text-indigo-500"
          >
            create a new account
          </Link>
        </p>
      </div>

      {/* Error display */}
      {formState.error && (
        <div className="rounded-md bg-red-50 p-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                Login Error
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{formState.error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      <form className="space-y-6" onSubmit={handleSubmit}>
        <div className="space-y-4">
          {/* Email */}
          <div>
            <label
              htmlFor="email"
              className="block text-sm font-medium text-gray-700"
            >
              Email address
            </label>
            <input
              id="email"
              name="email"
              type="email"
              autoComplete="email"
              required
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              className={`mt-1 block w-full appearance-none border px-3 py-2 ${
                validationErrors.email ? 'border-red-300' : 'border-gray-300'
              } rounded-md text-gray-900 placeholder-gray-500 focus:z-10 focus:border-indigo-500 focus:ring-indigo-500 focus:outline-none sm:text-sm`}
              placeholder="Enter your email"
            />
            {validationErrors.email && (
              <p className="mt-1 text-sm text-red-600">
                {validationErrors.email}
              </p>
            )}
          </div>

          {/* Password */}
          <div>
            <label
              htmlFor="password"
              className="block text-sm font-medium text-gray-700"
            >
              Password
            </label>
            <input
              id="password"
              name="password"
              type="password"
              autoComplete="current-password"
              required
              value={formData.password}
              onChange={(e) => handleInputChange('password', e.target.value)}
              className={`mt-1 block w-full appearance-none border px-3 py-2 ${
                validationErrors.password
                  ? 'border-red-300'
                  : 'border-gray-300'
              } rounded-md text-gray-900 placeholder-gray-500 focus:z-10 focus:border-indigo-500 focus:ring-indigo-500 focus:outline-none sm:text-sm`}
              placeholder="Enter your password"
            />
            {validationErrors.password && (
              <p className="mt-1 text-sm text-red-600">
                {validationErrors.password}
              </p>
            )}
          </div>
        </div>

        <div>
          <button
            type="submit"
            disabled={formState.loading}
            onClick={() => console.log('Button clicked!')}
            className="group relative flex w-full justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
          >
            {formState.loading ? 'Signing in...' : 'Sign in'}
          </button>
        </div>
      </form>
    </div>
  );
}
