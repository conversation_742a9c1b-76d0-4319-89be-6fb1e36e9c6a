'use client';

import { ReactNode, useCallback, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { AuthSession, UserPermissions, ROLE_PERMISSIONS } from '@/types/auth';
import { supabase } from '@/lib/supabase';
import { authStorage } from '@/lib/auth-storage';

interface ProtectedRouteProps {
  children: ReactNode;
  requiredPermissions?: Partial<UserPermissions>;
  fallback?: ReactNode;
  redirectTo?: string;
}

export function ProtectedRoute({
  children,
  requiredPermissions,
  fallback,
  redirectTo = '/auth/login',
}: ProtectedRouteProps) {
  const router = useRouter();
  const [session, setSession] = useState<AuthSession | null>(null);
  const [loading, setLoading] = useState(true);
  const [hasPermission, setHasPermission] = useState(false);

  const checkAuth = useCallback(async () => {
    try {
      // Check if we have a stored session using secure storage
      const storedSession = await authStorage.get();
      if (storedSession) {
        setSession(storedSession);
        setLoading(false);
        return;
      }

      // Check Supabase session
      const {
        data: { session: supabaseSession },
        error: sessionError,
      } = await supabase.auth.getSession();

      // Handle invalid refresh token error
      if (sessionError && sessionError.message.includes('Invalid Refresh Token')) {
        console.log('Invalid refresh token detected, clearing session and redirecting to login');
        await authStorage.clear();
        await supabase.auth.signOut();
        setLoading(false);
        router.push(redirectTo);
        return;
      }

      if (!supabaseSession) {
        setLoading(false);
        router.push(redirectTo);
        return;
      }

      // Create a basic session from Supabase data
      const basicSession = {
        user: supabaseSession.user,
        profile: null, // Will be loaded separately if needed
        organizations: [], // Will be loaded separately if needed
        current_organization: null,
        current_role: null,
      };

      setSession(basicSession);
      authStorage.store(basicSession);
    } catch (error) {
      console.error('Auth check error:', error);
      router.push(redirectTo);
    } finally {
      setLoading(false);
    }
  }, [redirectTo, router]);

  const checkPermissions = useCallback(() => {
    if (!session || !requiredPermissions || !session.current_role) {
      setHasPermission(false);
      return;
    }

    const userPermissions = ROLE_PERMISSIONS[session.current_role];

    // Check if user has all required permissions
    const hasAllPermissions = Object.entries(requiredPermissions).every(
      ([permission, required]) => {
        if (required === true) {
          return userPermissions[permission as keyof UserPermissions] === true;
        }
        return true;
      },
    );

    setHasPermission(hasAllPermissions);
  }, [session, requiredPermissions]);

  useEffect(() => {
    checkAuth();

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, supabaseSession) => {
      if (event === 'SIGNED_OUT' || !supabaseSession) {
        setSession(null);
        setLoading(false);
        router.push(redirectTo);
      } else if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
        await checkAuth();
      }
    });

    return () => subscription.unsubscribe();
  }, [checkAuth, redirectTo, router]);

  useEffect(() => {
    if (session && requiredPermissions) {
      checkPermissions();
    } else if (session) {
      setHasPermission(true);
    }
  }, [session, requiredPermissions, checkPermissions]);

  // Loading state
  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="mx-auto h-12 w-12 animate-spin rounded-full border-b-2 border-indigo-600"></div>
          <p className="mt-4 text-sm text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Not authenticated
  if (!session) {
    if (fallback) {
      return <>{fallback}</>;
    }
    return null; // Will redirect in useEffect
  }

  // Authenticated but insufficient permissions
  if (requiredPermissions && !hasPermission) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
            <svg
              className="h-6 w-6 text-red-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h2 className="mt-6 text-2xl font-bold text-gray-900">
            Access Denied
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            You don&apos;t have permission to access this page.
          </p>
          <button
            onClick={() => router.push('/dashboard')}
            className="mt-4 inline-flex items-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none"
          >
            Go to Dashboard
          </button>
        </div>
      </div>
    );
  }

  // Authenticated and has permissions
  return <>{children}</>;
}

export function useAuth() {
  const [session, setSession] = useState<AuthSession | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadSession = async () => {
      try {
        // Get stored session using secure storage
        const storedSession = await authStorage.get();
        if (storedSession) {
          setSession(storedSession);
        }
      } catch (error: any) {
        // Handle invalid refresh token error
        if (error?.message?.includes('Invalid Refresh Token')) {
          console.log('Invalid refresh token detected in useAuth, clearing session');
          await authStorage.clear();
          await supabase.auth.signOut();
        }
      } finally {
        setLoading(false);
      }
    };

    loadSession();

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((event, supabaseSession) => {
      if (event === 'SIGNED_OUT' || !supabaseSession) {
        setSession(null);
        authStorage.clear();
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  const signOut = async () => {
    await supabase.auth.signOut();
    setSession(null);
    authStorage.clear();
  };

  const updateSession = (newSession: AuthSession) => {
    setSession(newSession);
    authStorage.update(newSession);
  };

  return {
    session,
    loading,
    signOut,
    updateSession,
    user: session?.user || null,
    profile: session?.profile || null,
    currentOrganization: session?.current_organization || null,
    currentRole: session?.current_role || null,
    permissions:
      session && session.current_role
        ? ROLE_PERMISSIONS[session.current_role]
        : null,
  };
}
