import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import {
  CRMResponse,
  ClientWithRelations,
  ClientFormData,
  ClientFilters,
} from '@/types/crm';
import {
  validateICO,
  validateDIC,
  validateVATNumber,
  validateIBAN,
  validateEmail,
  validatePhone,
  getCountrySpecificFields,
  isEUCountry,
} from '@/lib/countries';

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SECRET_KEY!,
);

// Helper function to get authenticated user and organization
async function getAuthenticatedUser(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader?.startsWith('Bearer ')) {
    return { error: 'Authorization required', status: 401 };
  }

  const token = authHeader.substring(7);
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser(token);

  if (authError || !user) {
    return { error: 'Invalid authorization', status: 401 };
  }

  // Get user's current organization
  const { data: userOrg, error: orgError } = await supabase
    .from('user_organizations')
    .select('organization_id, role')
    .eq('user_id', user.id)
    .single();

  if (orgError || !userOrg) {
    return { error: 'No organization found', status: 403 };
  }

  return { user, organization_id: userOrg.organization_id, role: userOrg.role };
}

// Validate client data
function validateClientData(data: ClientFormData): string[] {
  const errors: string[] = [];

  // Required fields
  if (!data.name?.trim()) errors.push('Name is required');
  if (!data.country) errors.push('Country is required');
  if (!data.city?.trim()) errors.push('City is required');
  if (!data.type) errors.push('Client type is required');

  // Country-specific validation
  if (data.country === 'CZ') {
    if (data.ico && !validateICO(data.ico)) {
      errors.push('Invalid ICO format (must be 8 digits)');
    }
    if (data.dic && !validateDIC(data.dic)) {
      errors.push('Invalid DIC format (must be CZ followed by 8-10 digits)');
    }
  } else if (isEUCountry(data.country)) {
    if (data.vat_number && !validateVATNumber(data.vat_number, data.country)) {
      errors.push('Invalid VAT number format for selected country');
    }
  }

  // IBAN validation
  if (data.iban && !validateIBAN(data.iban)) {
    errors.push('Invalid IBAN format');
  }

  // Email validation
  if (data.invoice_email && !validateEmail(data.invoice_email)) {
    errors.push('Invalid email format');
  }

  // Contact validation
  data.contacts?.forEach((contact, index) => {
    if (!contact.name?.trim()) {
      errors.push(`Contact ${index + 1}: Name is required`);
    }
    if (contact.email && !validateEmail(contact.email)) {
      errors.push(`Contact ${index + 1}: Invalid email format`);
    }
    if (contact.phone && !validatePhone(contact.phone)) {
      errors.push(`Contact ${index + 1}: Invalid phone format`);
    }
  });

  return errors;
}

// GET /api/clients - List clients with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    const auth = await getAuthenticatedUser(request);
    if ('error' in auth) {
      return NextResponse.json<CRMResponse>(
        { success: false, error: auth.error },
        { status: auth.status },
      );
    }

    const { searchParams } = new URL(request.url);
    const filters: ClientFilters = {
      search: searchParams.get('search') || undefined,
      country: (searchParams.get('country') as any) || undefined,
      type: (searchParams.get('type') as any) || undefined,
      vat_status: (searchParams.get('vat_status') as any) || undefined,
      page: parseInt(searchParams.get('page') || '1'),
      limit: Math.min(parseInt(searchParams.get('limit') || '20'), 100),
      sort_by: (searchParams.get('sort_by') as any) || 'name',
      sort_order: (searchParams.get('sort_order') as any) || 'asc',
    };

    // Build query
    let query = supabase
      .from('clients')
      .select(
        `
        *,
        contacts:client_contacts(*),
        country_specifics:client_country_specifics(*)
      `,
      )
      .eq('organization_id', auth.organization_id);

    // Apply filters
    if (filters.search) {
      query = query.ilike('name', `%${filters.search}%`);
    }
    if (filters.country) {
      query = query.eq('country', filters.country);
    }
    if (filters.type) {
      query = query.eq('type', filters.type);
    }
    if (filters.vat_status) {
      query = query.eq('vat_status', filters.vat_status);
    }

    // Apply sorting
    const sortColumn = filters.sort_by || 'name';
    const sortOrder = filters.sort_order === 'desc' ? false : true;
    query = query.order(sortColumn, { ascending: sortOrder });

    // Apply pagination
    const page = filters.page || 1;
    const limit = filters.limit || 20;
    const offset = (page - 1) * limit;
    query = query.range(offset, offset + limit - 1);

    const { data: clients, error, count } = await query;

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json<CRMResponse>(
        { success: false, error: 'Failed to fetch clients' },
        { status: 500 },
      );
    }

    // Get statistics
    const { data: stats } = await supabase
      .from('clients')
      .select('country, type, vat_status')
      .eq('organization_id', auth.organization_id);

    const clientStats = {
      total_clients: count || 0,
      by_country: {},
      by_type: {},
      by_vat_status: {},
      recent_clients: clients?.slice(0, 5) || [],
    };

    // Calculate statistics
    stats?.forEach((client) => {
      clientStats.by_country[client.country] =
        (clientStats.by_country[client.country] || 0) + 1;
      clientStats.by_type[client.type] =
        (clientStats.by_type[client.type] || 0) + 1;
      if (client.vat_status) {
        clientStats.by_vat_status[client.vat_status] =
          (clientStats.by_vat_status[client.vat_status] || 0) + 1;
      }
    });

    const totalPages = Math.ceil((count || 0) / limit);

    return NextResponse.json<CRMResponse>({
      success: true,
      data: {
        clients: clients as ClientWithRelations[],
        stats: clientStats,
      },
      pagination: {
        page,
        limit,
        total: count || 0,
        total_pages: totalPages,
        has_next: page < totalPages,
        has_prev: page > 1,
      },
    });
  } catch (error) {
    console.error('Clients GET error:', error);
    return NextResponse.json<CRMResponse>(
      { success: false, error: 'Internal server error' },
      { status: 500 },
    );
  }
}

// POST /api/clients - Create new client
export async function POST(request: NextRequest) {
  try {
    const auth = await getAuthenticatedUser(request);
    if ('error' in auth) {
      return NextResponse.json<CRMResponse>(
        { success: false, error: auth.error },
        { status: auth.status },
      );
    }

    const body: ClientFormData = await request.json();

    // Validate data
    const validationErrors = validateClientData(body);
    if (validationErrors.length > 0) {
      return NextResponse.json<CRMResponse>(
        {
          success: false,
          error: 'Validation failed',
          details: validationErrors,
        },
        { status: 400 },
      );
    }

    // Start transaction
    const { data: client, error: clientError } = await supabase
      .from('clients')
      .insert({
        organization_id: auth.organization_id,
        type: body.type,
        name: body.name,
        country: body.country,
        street: body.street,
        city: body.city,
        postal_code: body.postal_code,
        address_country: body.country,
        invoice_street: body.use_same_address
          ? body.street
          : body.invoice_street,
        invoice_city: body.use_same_address ? body.city : body.invoice_city,
        invoice_postal_code: body.use_same_address
          ? body.postal_code
          : body.invoice_postal_code,
        invoice_country: body.use_same_address
          ? body.country
          : body.invoice_country,
        bank_name: body.bank_name,
        account_number: body.account_number,
        iban: body.iban,
        swift: body.swift,
        invoice_email: body.invoice_email,
        notes: body.notes,
        tags: body.tags,
        ico: body.ico,
        dic: body.dic,
        registration_number: body.registration_number,
        vat_status: body.vat_status,
        vat_number: body.vat_number,
        tax_id: body.tax_id,
        created_by: auth.user.id,
      })
      .select()
      .single();

    if (clientError) {
      console.error('Client creation error:', clientError);
      return NextResponse.json<CRMResponse>(
        { success: false, error: 'Failed to create client' },
        { status: 500 },
      );
    }

    // Create contacts
    if (body.contacts && body.contacts.length > 0) {
      const contactsToInsert = body.contacts.map((contact, index) => ({
        client_id: client.id,
        name: contact.name,
        email: contact.email,
        phone: contact.phone,
        position: contact.position,
        is_default_contact: contact.is_default_contact || index === 0,
      }));

      const { error: contactsError } = await supabase
        .from('client_contacts')
        .insert(contactsToInsert);

      if (contactsError) {
        console.error('Contacts creation error:', contactsError);
        // Don't fail the entire operation, just log the error
      }
    }

    // Fetch the complete client with relations
    const { data: completeClient } = await supabase
      .from('clients')
      .select(
        `
        *,
        contacts:client_contacts(*),
        country_specifics:client_country_specifics(*)
      `,
      )
      .eq('id', client.id)
      .single();

    return NextResponse.json<CRMResponse>({
      success: true,
      data: completeClient,
      message: 'Client created successfully',
    });
  } catch (error) {
    console.error('Clients POST error:', error);
    return NextResponse.json<CRMResponse>(
      { success: false, error: 'Internal server error' },
      { status: 500 },
    );
  }
}
