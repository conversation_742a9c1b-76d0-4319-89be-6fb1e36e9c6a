import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { CRMResponse, ClientWithRelations, ClientFormData } from '@/types/crm';
import {
  validateICO,
  validateDIC,
  validateVATNumber,
  validateIBAN,
  validateEmail,
  validatePhone,
  isEUCountry,
} from '@/lib/countries';

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SECRET_KEY!,
);

// Helper function to get authenticated user and organization
async function getAuthenticatedUser(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader?.startsWith('Bearer ')) {
    return { error: 'Authorization required', status: 401 };
  }

  const token = authHeader.substring(7);
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser(token);

  if (authError || !user) {
    return { error: 'Invalid authorization', status: 401 };
  }

  // Get user's current organization
  const { data: userOrg, error: orgError } = await supabase
    .from('user_organizations')
    .select('organization_id, role')
    .eq('user_id', user.id)
    .single();

  if (orgError || !userOrg) {
    return { error: 'No organization found', status: 403 };
  }

  return { user, organization_id: userOrg.organization_id, role: userOrg.role };
}

// Validate client data
function validateClientData(data: ClientFormData): string[] {
  const errors: string[] = [];

  // Required fields
  if (!data.name?.trim()) errors.push('Name is required');
  if (!data.country) errors.push('Country is required');
  if (!data.city?.trim()) errors.push('City is required');
  if (!data.type) errors.push('Client type is required');

  // Country-specific validation
  if (data.country === 'CZ') {
    if (data.ico && !validateICO(data.ico)) {
      errors.push('Invalid ICO format (must be 8 digits)');
    }
    if (data.dic && !validateDIC(data.dic)) {
      errors.push('Invalid DIC format (must be CZ followed by 8-10 digits)');
    }
  } else if (isEUCountry(data.country)) {
    if (data.vat_number && !validateVATNumber(data.vat_number, data.country)) {
      errors.push('Invalid VAT number format for selected country');
    }
  }

  // IBAN validation
  if (data.iban && !validateIBAN(data.iban)) {
    errors.push('Invalid IBAN format');
  }

  // Email validation
  if (data.invoice_email && !validateEmail(data.invoice_email)) {
    errors.push('Invalid email format');
  }

  // Contact validation
  data.contacts?.forEach((contact, index) => {
    if (!contact.name?.trim()) {
      errors.push(`Contact ${index + 1}: Name is required`);
    }
    if (contact.email && !validateEmail(contact.email)) {
      errors.push(`Contact ${index + 1}: Invalid email format`);
    }
    if (contact.phone && !validatePhone(contact.phone)) {
      errors.push(`Contact ${index + 1}: Invalid phone format`);
    }
  });

  return errors;
}

// GET /api/clients/[id] - Get single client
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const auth = await getAuthenticatedUser(request);
    if ('error' in auth) {
      return NextResponse.json<CRMResponse>(
        { success: false, error: auth.error },
        { status: auth.status },
      );
    }

    const { data: client, error } = await supabase
      .from('clients')
      .select(
        `
        *,
        contacts:client_contacts(*),
        country_specifics:client_country_specifics(*)
      `,
      )
      .eq('id', params.id)
      .eq('organization_id', auth.organization_id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json<CRMResponse>(
          { success: false, error: 'Client not found' },
          { status: 404 },
        );
      }
      console.error('Database error:', error);
      return NextResponse.json<CRMResponse>(
        { success: false, error: 'Failed to fetch client' },
        { status: 500 },
      );
    }

    return NextResponse.json<CRMResponse>({
      success: true,
      data: client as ClientWithRelations,
    });
  } catch (error) {
    console.error('Client GET error:', error);
    return NextResponse.json<CRMResponse>(
      { success: false, error: 'Internal server error' },
      { status: 500 },
    );
  }
}

// PUT /api/clients/[id] - Update client
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const auth = await getAuthenticatedUser(request);
    if ('error' in auth) {
      return NextResponse.json<CRMResponse>(
        { success: false, error: auth.error },
        { status: auth.status },
      );
    }

    const body: ClientFormData = await request.json();

    // Validate data
    const validationErrors = validateClientData(body);
    if (validationErrors.length > 0) {
      return NextResponse.json<CRMResponse>(
        {
          success: false,
          error: 'Validation failed',
          details: validationErrors,
        },
        { status: 400 },
      );
    }

    // Check if client exists and belongs to organization
    const { data: existingClient, error: checkError } = await supabase
      .from('clients')
      .select('id')
      .eq('id', params.id)
      .eq('organization_id', auth.organization_id)
      .single();

    if (checkError || !existingClient) {
      return NextResponse.json<CRMResponse>(
        { success: false, error: 'Client not found' },
        { status: 404 },
      );
    }

    // Update client
    const { data: client, error: clientError } = await supabase
      .from('clients')
      .update({
        type: body.type,
        name: body.name,
        country: body.country,
        street: body.street,
        city: body.city,
        postal_code: body.postal_code,
        address_country: body.country,
        invoice_street: body.use_same_address
          ? body.street
          : body.invoice_street,
        invoice_city: body.use_same_address ? body.city : body.invoice_city,
        invoice_postal_code: body.use_same_address
          ? body.postal_code
          : body.invoice_postal_code,
        invoice_country: body.use_same_address
          ? body.country
          : body.invoice_country,
        bank_name: body.bank_name,
        account_number: body.account_number,
        iban: body.iban,
        swift: body.swift,
        invoice_email: body.invoice_email,
        notes: body.notes,
        tags: body.tags,
        ico: body.ico,
        dic: body.dic,
        registration_number: body.registration_number,
        vat_status: body.vat_status,
        vat_number: body.vat_number,
        tax_id: body.tax_id,
        updated_at: new Date().toISOString(),
      })
      .eq('id', params.id)
      .select()
      .single();

    if (clientError) {
      console.error('Client update error:', clientError);
      return NextResponse.json<CRMResponse>(
        { success: false, error: 'Failed to update client' },
        { status: 500 },
      );
    }

    // Update contacts - delete existing and create new ones
    await supabase.from('client_contacts').delete().eq('client_id', params.id);

    if (body.contacts && body.contacts.length > 0) {
      const contactsToInsert = body.contacts.map((contact, index) => ({
        client_id: params.id,
        name: contact.name,
        email: contact.email,
        phone: contact.phone,
        position: contact.position,
        is_default_contact: contact.is_default_contact || index === 0,
      }));

      const { error: contactsError } = await supabase
        .from('client_contacts')
        .insert(contactsToInsert);

      if (contactsError) {
        console.error('Contacts update error:', contactsError);
        // Don't fail the entire operation, just log the error
      }
    }

    // Fetch the complete updated client with relations
    const { data: completeClient } = await supabase
      .from('clients')
      .select(
        `
        *,
        contacts:client_contacts(*),
        country_specifics:client_country_specifics(*)
      `,
      )
      .eq('id', params.id)
      .single();

    return NextResponse.json<CRMResponse>({
      success: true,
      data: completeClient,
      message: 'Client updated successfully',
    });
  } catch (error) {
    console.error('Client PUT error:', error);
    return NextResponse.json<CRMResponse>(
      { success: false, error: 'Internal server error' },
      { status: 500 },
    );
  }
}

// DELETE /api/clients/[id] - Delete client
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const auth = await getAuthenticatedUser(request);
    if ('error' in auth) {
      return NextResponse.json<CRMResponse>(
        { success: false, error: auth.error },
        { status: auth.status },
      );
    }

    // Check if client exists and belongs to organization
    const { data: existingClient, error: checkError } = await supabase
      .from('clients')
      .select('id')
      .eq('id', params.id)
      .eq('organization_id', auth.organization_id)
      .single();

    if (checkError || !existingClient) {
      return NextResponse.json<CRMResponse>(
        { success: false, error: 'Client not found' },
        { status: 404 },
      );
    }

    // Check if client has invoices
    const { data: invoices, error: invoiceError } = await supabase
      .from('invoices')
      .select('id')
      .eq('client_id', params.id)
      .limit(1);

    if (invoiceError) {
      console.error('Invoice check error:', invoiceError);
      return NextResponse.json<CRMResponse>(
        { success: false, error: 'Failed to check client dependencies' },
        { status: 500 },
      );
    }

    if (invoices && invoices.length > 0) {
      return NextResponse.json<CRMResponse>(
        {
          success: false,
          error: 'Cannot delete client with existing invoices',
        },
        { status: 400 },
      );
    }

    // Delete client (contacts and country_specifics will be deleted by CASCADE)
    const { error: deleteError } = await supabase
      .from('clients')
      .delete()
      .eq('id', params.id);

    if (deleteError) {
      console.error('Client delete error:', deleteError);
      return NextResponse.json<CRMResponse>(
        { success: false, error: 'Failed to delete client' },
        { status: 500 },
      );
    }

    return NextResponse.json<CRMResponse>({
      success: true,
      message: 'Client deleted successfully',
    });
  } catch (error) {
    console.error('Client DELETE error:', error);
    return NextResponse.json<CRMResponse>(
      { success: false, error: 'Internal server error' },
      { status: 500 },
    );
  }
}
