import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { fetchFromARES, validateICOChecksum } from '@/lib/ares';
import { ARESResponse } from '@/types/crm';

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SECRET_KEY!,
);

// Helper function to get authenticated user
async function getAuthenticatedUser(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader?.startsWith('Bearer ')) {
    return { error: 'Authorization required', status: 401 };
  }

  const token = authHeader.substring(7);
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser(token);

  if (authError || !user) {
    return { error: 'Invalid authorization', status: 401 };
  }

  return { user };
}

// POST /api/ares - Fetch business data from ARES
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const auth = await getAuthenticatedUser(request);
    if ('error' in auth) {
      return NextResponse.json<ARESResponse>(
        { success: false, error: auth.error },
        { status: auth.status },
      );
    }

    const body = await request.json();
    const { ico } = body;

    // Validate ICO format
    if (!ico || typeof ico !== 'string') {
      return NextResponse.json<ARESResponse>(
        { success: false, error: 'ICO is required' },
        { status: 400 },
      );
    }

    // Clean ICO (remove spaces and non-digits)
    const cleanICO = ico.replace(/\D/g, '');

    if (cleanICO.length !== 8) {
      return NextResponse.json<ARESResponse>(
        { success: false, error: 'ICO must be 8 digits' },
        { status: 400 },
      );
    }

    // Validate ICO checksum
    if (!validateICOChecksum(cleanICO)) {
      return NextResponse.json<ARESResponse>(
        { success: false, error: 'Invalid ICO checksum' },
        { status: 400 },
      );
    }

    // Fetch data from ARES
    const aresResponse = await fetchFromARES(cleanICO);

    // Return the response
    return NextResponse.json<ARESResponse>(aresResponse);
  } catch (error) {
    console.error('ARES API error:', error);

    if (error instanceof Error) {
      return NextResponse.json<ARESResponse>(
        { success: false, error: `ARES service error: ${error.message}` },
        { status: 500 },
      );
    }

    return NextResponse.json<ARESResponse>(
      { success: false, error: 'Internal server error' },
      { status: 500 },
    );
  }
}

// GET /api/ares?ico=12345678 - Alternative GET endpoint for ARES lookup
export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const auth = await getAuthenticatedUser(request);
    if ('error' in auth) {
      return NextResponse.json<ARESResponse>(
        { success: false, error: auth.error },
        { status: auth.status },
      );
    }

    const { searchParams } = new URL(request.url);
    const ico = searchParams.get('ico');

    // Validate ICO format
    if (!ico) {
      return NextResponse.json<ARESResponse>(
        { success: false, error: 'ICO parameter is required' },
        { status: 400 },
      );
    }

    // Clean ICO (remove spaces and non-digits)
    const cleanICO = ico.replace(/\D/g, '');

    if (cleanICO.length !== 8) {
      return NextResponse.json<ARESResponse>(
        { success: false, error: 'ICO must be 8 digits' },
        { status: 400 },
      );
    }

    // Validate ICO checksum
    if (!validateICOChecksum(cleanICO)) {
      return NextResponse.json<ARESResponse>(
        { success: false, error: 'Invalid ICO checksum' },
        { status: 400 },
      );
    }

    // Fetch data from ARES
    const aresResponse = await fetchFromARES(cleanICO);

    // Return the response
    return NextResponse.json<ARESResponse>(aresResponse);
  } catch (error) {
    console.error('ARES API error:', error);

    if (error instanceof Error) {
      return NextResponse.json<ARESResponse>(
        { success: false, error: `ARES service error: ${error.message}` },
        { status: 500 },
      );
    }

    return NextResponse.json<ARESResponse>(
      { success: false, error: 'Internal server error' },
      { status: 500 },
    );
  }
}
