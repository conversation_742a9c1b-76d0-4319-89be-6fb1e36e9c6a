import { CRMPage } from '@/components/crm/CRMPage';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';

export default function CRM() {
  return (
    <ProtectedRoute>
      <DashboardLayout
        title="Client Management"
        subtitle="Manage your clients and business relationships"
        breadcrumbs={[
          { label: 'Dashboard', href: '/en/dashboard' },
          { label: 'CRM' }
        ]}
      >
        <CRMPage />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
